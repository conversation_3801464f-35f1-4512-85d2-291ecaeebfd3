# 🚀 云原生学习路线图

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Cloud Native](https://img.shields.io/badge/Cloud%20Native-Learning%20Path-blue.svg)](https://cncf.io/)

> 为有编程基础但对云原生技术完全陌生的开发者设计的3-4个月集中学习路线图

## 📖 项目简介

这是一个专为**有编程基础的开发者**设计的云原生技术学习路线图，旨在帮助您在3-4个月内掌握云原生全栈技能。通过循序渐进的4个学习阶段，您将从容器化基础开始，逐步深入到Kubernetes编排、监控可观测性，最终达到生产级云原生应用的设计和部署能力。

## 🎯 学习目标

完成本学习路线图后，您将能够：

- ✅ 熟练使用Docker进行应用容器化
- ✅ 在Kubernetes上部署和管理微服务应用
- ✅ 构建完整的监控、日志和链路追踪体系
- ✅ 实施云原生安全最佳实践
- ✅ 设计和维护生产级CI/CD流水线
- ✅ 理解并应用云原生架构设计原则

## 🗺️ 学习路径概览

### 📅 4阶段学习计划 (总计14周)

| 阶段 | 主题 | 时长 | 核心技能 |
|------|------|------|----------|
| **第一阶段** | 容器化基础 | 3周 | Docker、容器化部署 |
| **第二阶段** | 容器编排 | 4周 | Kubernetes、微服务架构 |
| **第三阶段** | 监控与可观测性 | 3周 | Prometheus、日志分析 |
| **第四阶段** | 生产级实践 | 4周 | CI/CD、安全、治理 |

## 🚀 快速开始

### 前置要求

- 熟悉至少一种编程语言（推荐：Python、Java、Go、Node.js）
- 基本的Linux命令行操作
- Git版本控制基础
- 计算机网络基础知识

### 开始学习

1. **克隆项目**
   ```bash
   git clone https://github.com/gitwyy/cloud-native-learning.git
   cd cloud-native-learning-roadmap
   ```

2. **设置学习环境**
   ```bash
   # 查看环境设置指南
   cat docs/tools-setup.md
   ```

3. **开始第一阶段学习**
   ```bash
   # 查看详细学习路径
   cat docs/learning-path.md
   ```

## 📚 文档结构

```
docs/
├── learning-path.md     # 📋 详细的分阶段学习路线图
├── concepts.md          # 🧠 云原生核心概念解释
├── tools-setup.md       # 🔧 开发环境和工具安装指南
├── resources.md         # 📖 学习资源推荐
└── troubleshooting.md   # 🚨 常见问题解决方案
```

## 🛠️ 实践项目

每个学习阶段都包含动手实践项目：

- **第一阶段**: 将传统Web应用容器化
- **第二阶段**: 在Kubernetes上部署微服务应用
- **第三阶段**: 为应用建立完整监控体系
- **第四阶段**: 构建端到端CI/CD流水线

详细项目说明请查看 [`projects/`](./projects) 目录。

## 📈 学习进度跟踪

使用以下检查清单跟踪您的学习进度：

- [ ] **第1周**: Docker基础和容器化原理
- [ ] **第2周**: Docker Compose和多容器应用
- [ ] **第3周**: 容器化项目实践
- [ ] **第4-5周**: Kubernetes基础和集群管理
- [ ] **第6-7周**: 服务发现和微服务部署
- [ ] **第8周**: 微服务架构实践
- [ ] **第9-10周**: 监控体系建设
- [ ] **第11周**: 日志和链路追踪
- [ ] **第12周**: 性能优化实践
- [ ] **第13-14周**: CI/CD和生产级部署

## 🤝 贡献指南

欢迎为这个学习路线图贡献内容！请查看 [CONTRIBUTING.md](./CONTRIBUTING.md) 了解如何参与。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](./LICENSE) 文件了解详情。

## 🆘 获取帮助

- 📖 查看 [常见问题](./docs/troubleshooting.md)
- 💬 在 Issues 中提问
- 📧 联系维护者

---

**开始您的云原生学习之旅吧！** 🌟

> 记住：学习云原生技术不是一蹴而就的过程，坚持实践和持续学习是成功的关键。