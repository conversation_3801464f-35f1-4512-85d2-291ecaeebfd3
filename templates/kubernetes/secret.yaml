# ==============================================================================
# Kubernetes Secret 模板
# 敏感信息管理
# ==============================================================================

# Opaque Secret - 通用敏感数据
apiVersion: v1
kind: Secret
metadata:
  name: myapp-secrets
  namespace: default
  labels:
    app: myapp
type: Opaque
data:
  # Base64 编码的敏感信息
  # 使用命令生成: echo -n 'your-value' | base64
  database-url: ********************************************************************
  api-key: eW91ci1hcGkta2V5LWhlcmU=
  secret-key: eW91ci1zZWNyZXQta2V5LWhlcmU=
  jwt-secret: and1dC1zZWNyZXQtZm9yLXNpZ25pbmctdG9rZW5z
  encryption-key: ZW5jcnlwdGlvbi1rZXktZm9yLWRhdGEtcHJvdGVjdGlvbg==

---
# Docker Registry Secret - 私有镜像仓库认证
apiVersion: v1
kind: Secret
metadata:
  name: myapp-registry-secret
  namespace: default
  labels:
    app: myapp
type: kubernetes.io/dockerconfigjson
data:
  # Docker配置JSON的base64编码
  # 生成方式: kubectl create secret docker-registry myapp-registry-secret \
  #   --docker-server=your-registry.com \
  #   --docker-username=your-username \
  #   --docker-password=your-password \
  #   --docker-email=<EMAIL> \
  #   --dry-run=client -o yaml
  .dockerconfigjson: ********************************************************************************************************************************************************************************************************************

---
# TLS Secret - HTTPS证书
apiVersion: v1
kind: Secret
metadata:
  name: myapp-tls-secret
  namespace: default
  labels:
    app: myapp
type: kubernetes.io/tls
data:
  # TLS证书和私钥的base64编码
  # 生成方式: 
  # kubectl create secret tls myapp-tls-secret \
  #   --cert=path/to/tls.crt \
  #   --key=path/to/tls.key \
  #   --dry-run=client -o yaml
  tls.crt: LS0tLS1CRUdJTi...  # 证书内容
  tls.key: LS0tLS1CRUdJTi...  # 私钥内容

---
# Service Account Token Secret
apiVersion: v1
kind: Secret
metadata:
  name: myapp-sa-token
  namespace: default
  labels:
    app: myapp
  annotations:
    kubernetes.io/service-account.name: myapp-service-account
type: kubernetes.io/service-account-token

---
# Basic Auth Secret - HTTP基础认证
apiVersion: v1
kind: Secret
metadata:
  name: myapp-basic-auth
  namespace: default
  labels:
    app: myapp
type: kubernetes.io/basic-auth
data:
  # 用户名和密码的base64编码
  username: YWRtaW4=  # admin
  password: cGFzc3dvcmQ=  # password

---
# SSH Auth Secret - SSH密钥认证
apiVersion: v1
kind: Secret
metadata:
  name: myapp-ssh-auth
  namespace: default
  labels:
    app: myapp
type: kubernetes.io/ssh-auth
data:
  # SSH私钥的base64编码
  ssh-privatekey: LS0tLS1CRUdJTi...

---
# 数据库连接 Secret
apiVersion: v1
kind: Secret
metadata:
  name: myapp-database
  namespace: default
  labels:
    app: myapp
    type: database
type: Opaque
data:
  # 数据库连接信息
  DB_HOST: bXlhcHAtZGF0YWJhc2U=  # myapp-database
  DB_PORT: NTQzMg==  # 5432
  DB_NAME: bXlhcHBkYg==  # myappdb
  DB_USER: cG9zdGdyZXM=  # postgres
  DB_PASSWORD: c2VjdXJlcGFzc3dvcmQ=  # securepassword
  DB_SSL_MODE: cmVxdWlyZQ==  # require

---
# Redis连接 Secret
apiVersion: v1
kind: Secret
metadata:
  name: myapp-redis
  namespace: default
  labels:
    app: myapp
    type: cache
type: Opaque
data:
  REDIS_HOST: bXlhcHAtcmVkaXM=  # myapp-redis
  REDIS_PORT: NjM3OQ==  # 6379
  REDIS_PASSWORD: cmVkaXNwYXNz  # redispass
  REDIS_DB: MA==  # 0

---
# 第三方服务API密钥
apiVersion: v1
kind: Secret
metadata:
  name: myapp-external-apis
  namespace: default
  labels:
    app: myapp
    type: external-apis
type: Opaque
data:
  AWS_ACCESS_KEY_ID: WU9VUl9BV1NfQUNDRVNTX0tFWQ==
  AWS_SECRET_ACCESS_KEY: WU9VUl9BV1NfU0VDUkVUX0tFWQ==
  STRIPE_API_KEY: c2tfbGl2ZV95b3VyX3N0cmlwZV9rZXk=
  SENDGRID_API_KEY: U0cuWW91cl9TZW5kR3JpZF9BUElfS2V5
  GOOGLE_MAPS_API_KEY: WW91cl9Hb29nbGVfTWFwc19BUElfS2V5