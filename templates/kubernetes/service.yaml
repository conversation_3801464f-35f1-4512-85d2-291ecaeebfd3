# ==============================================================================
# Kubernetes Service 模板
# 服务发现和负载均衡配置
# ==============================================================================

# ClusterIP Service - 内部访问
apiVersion: v1
kind: Service
metadata:
  name: myapp-service
  namespace: default
  labels:
    app: myapp
    tier: backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  selector:
    app: myapp

---
# NodePort Service - 外部访问（测试用）
apiVersion: v1
kind: Service
metadata:
  name: myapp-nodeport
  namespace: default
  labels:
    app: myapp
    tier: backend
spec:
  type: NodePort
  ports:
  - name: http
    port: 80
    targetPort: http
    nodePort: 30080
    protocol: TCP
  selector:
    app: myapp

---
# LoadBalancer Service - 生产环境外部访问
apiVersion: v1
kind: Service
metadata:
  name: myapp-loadbalancer
  namespace: default
  labels:
    app: myapp
    tier: backend
  annotations:
    # AWS Load Balancer 配置
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    # GCP Load Balancer 配置
    # cloud.google.com/load-balancer-type: "External"
    # Azure Load Balancer 配置
    # service.beta.kubernetes.io/azure-load-balancer-internal: "false"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: https
    port: 443
    targetPort: https
    protocol: TCP
  selector:
    app: myapp
  loadBalancerSourceRanges:
  - 0.0.0.0/0  # 允许所有IP访问，生产环境应限制

---
# Headless Service - 用于StatefulSet或服务发现
apiVersion: v1
kind: Service
metadata:
  name: myapp-headless
  namespace: default
  labels:
    app: myapp
    tier: backend
spec:
  type: ClusterIP
  clusterIP: None  # 无头服务
  ports:
  - name: http
    port: 3000
    targetPort: http
    protocol: TCP
  selector:
    app: myapp

---
# ExternalName Service - 外部服务映射
apiVersion: v1
kind: Service
metadata:
  name: myapp-external-db
  namespace: default
  labels:
    app: myapp
    tier: database
spec:
  type: ExternalName
  externalName: db.example.com
  ports:
  - name: postgres
    port: 5432
    protocol: TCP