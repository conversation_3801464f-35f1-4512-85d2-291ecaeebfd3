# 🤝 贡献指南

感谢您对云原生学习路线图项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 📝 改进文档内容
- 🐛 报告和修复错误
- ✨ 添加新的学习资源
- 🔧 改进项目示例
- 💡 提出新的学习路径建议

## 🚀 如何开始贡献

### 1. Fork 项目

点击页面右上角的 "Fork" 按钮，将项目复制到您的 GitHub 账户。

### 2. 克隆到本地

```bash
git clone https://github.com/gitwyy/cloud-native-learning.git
cd cloud-native-learning
```

### 3. 创建功能分支

```bash
git checkout -b feature/your-feature-name
# 或者修复分支
git checkout -b fix/your-fix-name
```

### 4. 进行修改

请确保您的修改：
- 遵循项目的代码风格
- 包含适当的文档更新
- 通过相关测试

### 5. 提交更改

```bash
git add .
git commit -m "feat: 添加新的学习资源"
# 或者
git commit -m "fix: 修复文档中的错误链接"
```

### 6. 推送到您的 Fork

```bash
git push origin feature/your-feature-name
```

### 7. 创建 Pull Request

在 GitHub 上创建 Pull Request，详细描述您的更改。

## 📋 贡献类型

### 文档改进
- 修正拼写和语法错误
- 改进说明的清晰度
- 添加缺失的信息
- 更新过时的内容

### 学习资源
- 推荐高质量的学习材料
- 添加实用的工具和网站
- 分享学习经验和最佳实践

### 项目示例
- 改进现有示例代码
- 添加新的实践项目
- 优化配置文件
- 增强错误处理

### 问题报告
使用 GitHub Issues 报告：
- 文档错误或不清楚的地方
- 代码示例中的问题
- 安装或设置问题
- 功能请求

## 📝 提交信息规范

请使用以下格式的提交信息：

```
<类型>: <简短描述>

<详细描述>（可选）
```

类型包括：
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 🎯 贡献重点领域

我们特别欢迎以下方面的贡献：

1. **多语言支持** - 将内容翻译成其他语言
2. **云平台适配** - 添加 AWS、Azure、GCP 等平台的具体指导
3. **工具更新** - 跟进最新的云原生工具和技术
4. **实战案例** - 分享真实的项目经验和案例
5. **性能优化** - 改进示例项目的性能和最佳实践

## 🔍 代码审查

所有的 Pull Request 都会经过代码审查：

- 确保代码质量和一致性
- 验证文档的准确性
- 检查是否遵循最佳实践
- 确保不包含敏感信息

## 📞 联系方式

如果您有任何问题或建议，可以通过以下方式联系我们：

- 创建 GitHub Issue
- 在 Pull Request 中讨论
- 发送邮件给项目维护者

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！您的贡献让这个学习路线图变得更好。

---

**再次感谢您的贡献！** 🌟
