{"name": "user-service", "version": "1.0.0", "description": "用户管理微服务 - 处理用户注册、登录、个人信息管理", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "echo 'No build step required for Node.js'", "docker:build": "docker build -t user-service:latest .", "docker:run": "docker run -p 3001:3001 user-service:latest"}, "keywords": ["microservice", "user-management", "authentication", "nodejs", "express", "kubernetes"], "author": "Cloud Native Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "joi": "^17.9.2", "pg": "^8.11.0", "redis": "^4.6.7", "winston": "^3.9.0", "express-winston": "^4.2.0", "dotenv": "^16.1.4", "uuid": "^9.0.0", "nodemailer": "^6.9.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.3", "prometheus-client": "^1.0.0", "jaeger-client": "^3.19.0", "express-opentracing": "^0.1.1"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.42.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "@types/jest": "^29.5.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ecommerce-platform.git"}, "bugs": {"url": "https://github.com/your-org/ecommerce-platform/issues"}, "homepage": "https://github.com/your-org/ecommerce-platform#readme"}