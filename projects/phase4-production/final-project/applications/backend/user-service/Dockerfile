# 多阶段构建的安全Node.js镜像
# 用户服务容器化配置

# 第一阶段：构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖（包括开发依赖）
RUN npm ci --only=production && npm cache clean --force

# 第二阶段：运行阶段
FROM node:18-alpine AS runtime

# 安全标签
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="用户管理微服务"
LABEL security.scan="trivy"

# 更新系统包并安装必要工具
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
        dumb-init \
        curl \
        ca-certificates && \
    rm -rf /var/cache/apk/*

# 创建非特权用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001 -G nodejs

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p logs tmp && \
    chown -R nodeuser:nodejs /app

# 复制依赖
COPY --from=builder --chown=nodeuser:nodejs /app/node_modules ./node_modules

# 复制应用代码
COPY --chown=nodeuser:nodejs . .

# 设置正确的文件权限
RUN chmod -R 755 /app && \
    chmod -R 777 /app/logs /app/tmp

# 切换到非特权用户
USER nodeuser

# 暴露端口（使用非特权端口）
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3001

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "src/index.js"]
