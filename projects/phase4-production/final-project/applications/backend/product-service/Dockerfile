# 多阶段构建的安全Go镜像
# 商品服务容器化配置

# 第一阶段：构建阶段
FROM golang:1.19-alpine AS builder

# 安全标签
LABEL stage=builder

# 安装必要的构建工具
RUN apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o product-service main.go

# 第二阶段：运行阶段
FROM scratch AS runtime

# 安全标签
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="商品管理微服务"
LABEL security.scan="trivy"

# 从builder阶段复制CA证书
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# 从builder阶段复制时区信息
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# 复制编译好的二进制文件
COPY --from=builder /app/product-service /product-service

# 创建非root用户（在scratch镜像中通过文件系统实现）
COPY --from=builder /etc/passwd /etc/passwd
COPY --from=builder /etc/group /etc/group

# 设置用户
USER 65534:65534

# 暴露端口
EXPOSE 3002

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/product-service", "-health-check"]

# 设置环境变量
ENV GIN_MODE=release
ENV PORT=3002

# 启动应用
ENTRYPOINT ["/product-service"]
