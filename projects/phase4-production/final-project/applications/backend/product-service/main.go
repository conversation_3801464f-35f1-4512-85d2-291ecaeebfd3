package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"product-service/internal/config"
	"product-service/internal/database"
	"product-service/internal/handlers"
	"product-service/internal/middleware"
	"product-service/internal/repository"
	"product-service/internal/service"
	"product-service/internal/tracing"
)

// @title 商品服务 API
// @version 1.0
// @description 商品管理微服务的API文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:3002
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

var (
	// Prometheus指标
	httpRequestsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "HTTP请求总数",
		},
		[]string{"method", "endpoint", "status"},
	)

	httpRequestDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "HTTP请求持续时间",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	logger = logrus.New()
)

func init() {
	// 注册Prometheus指标
	prometheus.MustRegister(httpRequestsTotal)
	prometheus.MustRegister(httpRequestDuration)

	// 配置日志
	logger.SetFormatter(&logrus.JSONFormatter{})
	logger.SetLevel(logrus.InfoLevel)
	
	if os.Getenv("GIN_MODE") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}
}

func main() {
	// 加载配置
	cfg := config.Load()
	
	// 初始化分布式追踪
	tracer, closer, err := tracing.Init("product-service")
	if err != nil {
		logger.Fatalf("初始化追踪失败: %v", err)
	}
	defer closer.Close()

	// 连接数据库
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		logger.Fatalf("数据库连接失败: %v", err)
	}

	// 连接Redis
	redisClient, err := database.ConnectRedis(cfg.RedisURL)
	if err != nil {
		logger.Fatalf("Redis连接失败: %v", err)
	}

	// 初始化仓库层
	productRepo := repository.NewProductRepository(db, redisClient)

	// 初始化服务层
	productService := service.NewProductService(productRepo, logger)

	// 初始化处理器
	productHandler := handlers.NewProductHandler(productService, logger)

	// 创建Gin路由器
	router := gin.New()

	// 中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	router.Use(middleware.Metrics(httpRequestsTotal, httpRequestDuration))
	router.Use(middleware.Tracing(tracer))

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "product-service",
			"version":   "1.0.0",
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		})
	})

	// 就绪检查
	router.GET("/ready", func(c *gin.Context) {
		// 检查数据库连接
		sqlDB, err := db.DB()
		if err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "not ready",
				"error":  "database connection error",
			})
			return
		}

		if err := sqlDB.Ping(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "not ready",
				"error":  "database ping failed",
			})
			return
		}

		// 检查Redis连接
		if err := redisClient.Ping(context.Background()).Err(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "not ready",
				"error":  "redis ping failed",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status":    "ready",
			"service":   "product-service",
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		})
	})

	// Prometheus指标端点
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// API文档
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 服务信息
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service":     "product-service",
			"version":     "1.0.0",
			"description": "商品管理微服务",
			"endpoints": gin.H{
				"health":  "/health",
				"ready":   "/ready",
				"metrics": "/metrics",
				"docs":    "/swagger/index.html",
				"api":     "/api/v1",
			},
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 商品路由
		products := v1.Group("/products")
		{
			products.GET("", productHandler.GetProducts)
			products.GET("/:id", productHandler.GetProduct)
			products.POST("", middleware.Auth(), productHandler.CreateProduct)
			products.PUT("/:id", middleware.Auth(), productHandler.UpdateProduct)
			products.DELETE("/:id", middleware.Auth(), productHandler.DeleteProduct)
			products.GET("/search", productHandler.SearchProducts)
			products.GET("/categories", productHandler.GetCategories)
			products.GET("/category/:category", productHandler.GetProductsByCategory)
		}

		// 库存路由
		inventory := v1.Group("/inventory")
		{
			inventory.GET("/:productId", productHandler.GetInventory)
			inventory.PUT("/:productId", middleware.Auth(), productHandler.UpdateInventory)
			inventory.POST("/:productId/reserve", middleware.Auth(), productHandler.ReserveInventory)
			inventory.POST("/:productId/release", middleware.Auth(), productHandler.ReleaseInventory)
		}
	}

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%s", cfg.Port),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// 启动服务器
	go func() {
		logger.Infof("商品服务启动在端口 %s", cfg.Port)
		logger.Infof("API文档: http://localhost:%s/swagger/index.html", cfg.Port)
		logger.Infof("健康检查: http://localhost:%s/health", cfg.Port)
		logger.Infof("指标端点: http://localhost:%s/metrics", cfg.Port)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭服务器...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("服务器强制关闭: %v", err)
	}

	// 关闭数据库连接
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.Close()
	}

	// 关闭Redis连接
	redisClient.Close()

	logger.Info("服务器已关闭")
}
