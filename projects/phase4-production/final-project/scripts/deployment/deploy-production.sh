#!/bin/bash

# 生产环境部署脚本
# 部署完整的云原生电商平台到生产环境

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
NAMESPACE="${NAMESPACE:-ecommerce-prod}"
HELM_RELEASE="${HELM_RELEASE:-ecommerce-platform}"
CHART_PATH="${CHART_PATH:-./infrastructure/helm-charts/ecommerce-platform}"
VALUES_FILE="${VALUES_FILE:-./infrastructure/helm-charts/ecommerce-platform/values-production.yaml}"
TIMEOUT="${TIMEOUT:-600s}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    local tools=("kubectl" "helm" "docker")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少以下工具: ${missing_tools[*]}"
        exit 1
    fi
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查Helm
    if ! helm version &> /dev/null; then
        log_error "Helm未正确安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建命名空间
create_namespace() {
    log_info "创建命名空间: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 已存在"
    else
        kubectl create namespace "$NAMESPACE"
        
        # 添加安全标签
        kubectl label namespace "$NAMESPACE" \
            pod-security.kubernetes.io/enforce=restricted \
            pod-security.kubernetes.io/audit=restricted \
            pod-security.kubernetes.io/warn=restricted \
            --overwrite
        
        log_success "命名空间 $NAMESPACE 创建完成"
    fi
}

# 添加Helm仓库
add_helm_repos() {
    log_info "添加Helm仓库..."
    
    # 添加必要的Helm仓库
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo add grafana https://grafana.github.io/helm-charts
    helm repo add jaegertracing https://jaegertracing.github.io/helm-charts
    helm repo add istio https://istio-release.storage.googleapis.com/charts
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    
    # 更新仓库
    helm repo update
    
    log_success "Helm仓库添加完成"
}

# 安装基础设施组件
install_infrastructure() {
    log_info "安装基础设施组件..."
    
    # 安装Ingress Controller
    if ! helm list -n ingress-nginx | grep -q ingress-nginx; then
        log_info "安装NGINX Ingress Controller..."
        helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
            --namespace ingress-nginx \
            --create-namespace \
            --set controller.replicaCount=3 \
            --set controller.resources.requests.cpu=100m \
            --set controller.resources.requests.memory=90Mi \
            --set controller.resources.limits.cpu=500m \
            --set controller.resources.limits.memory=512Mi \
            --wait --timeout="$TIMEOUT"
        log_success "NGINX Ingress Controller安装完成"
    fi
    
    # 安装Cert Manager
    if ! helm list -n cert-manager | grep -q cert-manager; then
        log_info "安装Cert Manager..."
        helm upgrade --install cert-manager jetstack/cert-manager \
            --namespace cert-manager \
            --create-namespace \
            --set installCRDs=true \
            --wait --timeout="$TIMEOUT"
        log_success "Cert Manager安装完成"
    fi
    
    # 安装Prometheus Operator
    if ! helm list -n monitoring | grep -q kube-prometheus-stack; then
        log_info "安装Prometheus监控栈..."
        helm upgrade --install kube-prometheus-stack prometheus-community/kube-prometheus-stack \
            --namespace monitoring \
            --create-namespace \
            --set prometheus.prometheusSpec.retention=30d \
            --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
            --set grafana.persistence.enabled=true \
            --set grafana.persistence.size=10Gi \
            --wait --timeout="$TIMEOUT"
        log_success "Prometheus监控栈安装完成"
    fi
}

# 构建和推送镜像
build_and_push_images() {
    log_info "构建和推送应用镜像..."
    
    local services=("user-service" "product-service" "order-service" "payment-service" "web-app" "admin-panel")
    local registry="${DOCKER_REGISTRY:-your-registry.com}"
    local tag="${IMAGE_TAG:-1.0.0}"
    
    for service in "${services[@]}"; do
        log_info "构建镜像: $service"
        
        local dockerfile_path
        if [[ "$service" == "web-app" || "$service" == "admin-panel" ]]; then
            dockerfile_path="./applications/frontend/$service"
        else
            dockerfile_path="./applications/backend/$service"
        fi
        
        if [ -d "$dockerfile_path" ]; then
            docker build -t "$registry/$service:$tag" "$dockerfile_path"
            docker push "$registry/$service:$tag"
            log_success "镜像 $service 构建并推送完成"
        else
            log_warning "服务目录不存在: $dockerfile_path"
        fi
    done
}

# 运行安全扫描
run_security_scan() {
    log_info "运行安全扫描..."
    
    # 扫描镜像漏洞
    if command -v trivy &> /dev/null; then
        local services=("user-service" "product-service" "order-service" "payment-service")
        local registry="${DOCKER_REGISTRY:-your-registry.com}"
        local tag="${IMAGE_TAG:-1.0.0}"
        
        for service in "${services[@]}"; do
            log_info "扫描镜像: $service"
            trivy image --exit-code 1 --severity HIGH,CRITICAL "$registry/$service:$tag" || {
                log_error "镜像 $service 存在高危漏洞"
                exit 1
            }
        done
        
        log_success "镜像安全扫描通过"
    else
        log_warning "Trivy未安装，跳过镜像扫描"
    fi
}

# 部署应用
deploy_application() {
    log_info "部署应用到生产环境..."
    
    # 检查values文件
    if [ ! -f "$VALUES_FILE" ]; then
        log_warning "生产环境配置文件不存在，使用默认配置"
        VALUES_FILE="./infrastructure/helm-charts/ecommerce-platform/values.yaml"
    fi
    
    # 更新Helm依赖
    helm dependency update "$CHART_PATH"
    
    # 部署应用
    helm upgrade --install "$HELM_RELEASE" "$CHART_PATH" \
        --namespace "$NAMESPACE" \
        --values "$VALUES_FILE" \
        --set global.environment=production \
        --set global.imageRegistry="${DOCKER_REGISTRY:-your-registry.com}" \
        --set global.domain="${DOMAIN:-ecommerce.example.com}" \
        --wait --timeout="$TIMEOUT"
    
    log_success "应用部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    # 等待Pod就绪
    log_info "等待Pod就绪..."
    kubectl wait --for=condition=ready pod \
        --selector=app.kubernetes.io/instance="$HELM_RELEASE" \
        --namespace="$NAMESPACE" \
        --timeout=300s
    
    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get pods,svc,ingress -n "$NAMESPACE"
    
    # 运行健康检查
    log_info "运行健康检查..."
    local services=("user-service" "product-service" "order-service" "payment-service")
    
    for service in "${services[@]}"; do
        local service_name="${HELM_RELEASE}-${service}"
        local port
        
        case $service in
            "user-service") port=3001 ;;
            "product-service") port=3002 ;;
            "order-service") port=3003 ;;
            "payment-service") port=3004 ;;
        esac
        
        if kubectl get service "$service_name" -n "$NAMESPACE" &> /dev/null; then
            # 端口转发测试
            kubectl port-forward "service/$service_name" "$port:$port" -n "$NAMESPACE" &
            local pf_pid=$!
            
            sleep 5
            
            if curl -f "http://localhost:$port/health" &> /dev/null; then
                log_success "服务 $service 健康检查通过"
            else
                log_error "服务 $service 健康检查失败"
            fi
            
            kill $pf_pid 2>/dev/null || true
        else
            log_warning "服务 $service_name 不存在"
        fi
    done
}

# 配置监控和告警
setup_monitoring() {
    log_info "配置监控和告警..."
    
    # 应用ServiceMonitor
    kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: ecommerce-services
  namespace: $NAMESPACE
  labels:
    app.kubernetes.io/instance: $HELM_RELEASE
spec:
  selector:
    matchLabels:
      app.kubernetes.io/instance: $HELM_RELEASE
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
EOF
    
    log_success "监控配置完成"
}

# 显示部署信息
show_deployment_info() {
    log_info "部署信息:"
    echo ""
    echo "命名空间: $NAMESPACE"
    echo "Helm发布: $HELM_RELEASE"
    echo "图表路径: $CHART_PATH"
    echo "配置文件: $VALUES_FILE"
    echo ""
    
    # 获取Ingress信息
    local ingress_ip
    ingress_ip=$(kubectl get ingress -n "$NAMESPACE" -o jsonpath='{.items[0].status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    
    if [ "$ingress_ip" != "pending" ] && [ -n "$ingress_ip" ]; then
        echo "应用访问地址:"
        echo "  Web应用: https://${DOMAIN:-ecommerce.example.com}"
        echo "  管理后台: https://admin.${DOMAIN:-ecommerce.example.com}"
        echo "  Ingress IP: $ingress_ip"
    else
        echo "Ingress IP正在分配中..."
    fi
    
    echo ""
    echo "监控地址:"
    echo "  Grafana: kubectl port-forward svc/kube-prometheus-stack-grafana 3000:80 -n monitoring"
    echo "  Prometheus: kubectl port-forward svc/kube-prometheus-stack-prometheus 9090:9090 -n monitoring"
    echo ""
    echo "有用的命令:"
    echo "  查看Pod状态: kubectl get pods -n $NAMESPACE"
    echo "  查看日志: kubectl logs -f deployment/<service-name> -n $NAMESPACE"
    echo "  删除部署: helm uninstall $HELM_RELEASE -n $NAMESPACE"
}

# 主函数
main() {
    log_info "开始生产环境部署..."
    
    check_dependencies
    create_namespace
    add_helm_repos
    install_infrastructure
    build_and_push_images
    run_security_scan
    deploy_application
    verify_deployment
    setup_monitoring
    show_deployment_info
    
    log_success "生产环境部署完成！"
}

# 执行主函数
main "$@"
