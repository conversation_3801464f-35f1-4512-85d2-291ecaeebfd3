apiVersion: v2
name: ecommerce-platform
description: 云原生电商平台 - 完整的微服务应用Helm图表
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://github.com/your-org/ecommerce-platform
sources:
  - https://github.com/your-org/ecommerce-platform
maintainers:
  - name: Cloud Native Team
    email: <EMAIL>
    url: https://github.com/your-org
keywords:
  - ecommerce
  - microservices
  - cloud-native
  - kubernetes
  - helm
annotations:
  category: Application
  licenses: MIT
  images: |
    - name: user-service
      image: your-registry/user-service:1.0.0
    - name: product-service
      image: your-registry/product-service:1.0.0
    - name: order-service
      image: your-registry/order-service:1.0.0
    - name: payment-service
      image: your-registry/payment-service:1.0.0
    - name: web-app
      image: your-registry/web-app:1.0.0
    - name: admin-panel
      image: your-registry/admin-panel:1.0.0

dependencies:
  - name: postgresql
    version: "12.6.6"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
    tags:
      - database
  - name: redis
    version: "17.11.3"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
    tags:
      - cache
  - name: rabbitmq
    version: "12.0.1"
    repository: https://charts.bitnami.com/bitnami
    condition: rabbitmq.enabled
    tags:
      - messaging
  - name: elasticsearch
    version: "19.10.0"
    repository: https://charts.bitnami.com/bitnami
    condition: elasticsearch.enabled
    tags:
      - search
  - name: prometheus
    version: "23.1.0"
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
    tags:
      - monitoring
  - name: grafana
    version: "6.57.4"
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled
    tags:
      - monitoring
  - name: jaeger
    version: "0.71.2"
    repository: https://jaegertracing.github.io/helm-charts
    condition: tracing.jaeger.enabled
    tags:
      - tracing
  - name: istio-base
    version: "1.18.0"
    repository: https://istio-release.storage.googleapis.com/charts
    condition: serviceMesh.istio.enabled
    tags:
      - service-mesh
  - name: istiod
    version: "1.18.0"
    repository: https://istio-release.storage.googleapis.com/charts
    condition: serviceMesh.istio.enabled
    tags:
      - service-mesh
