# 云原生电商平台默认配置值

# 全局配置
global:
  # 镜像仓库配置
  imageRegistry: "your-registry.com"
  imagePullSecrets: []
  storageClass: "fast-ssd"
  
  # 环境配置
  environment: "production"
  domain: "ecommerce.example.com"
  
  # 安全配置
  security:
    enabled: true
    podSecurityStandards: "restricted"
    networkPolicies: true
    rbac: true

# 应用服务配置
services:
  # 用户服务
  userService:
    enabled: true
    name: user-service
    image:
      repository: user-service
      tag: "1.0.0"
      pullPolicy: IfNotPresent
    replicaCount: 3
    port: 3001
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
    env:
      NODE_ENV: production
      LOG_LEVEL: info
    
  # 商品服务
  productService:
    enabled: true
    name: product-service
    image:
      repository: product-service
      tag: "1.0.0"
      pullPolicy: IfNotPresent
    replicaCount: 3
    port: 3002
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
    env:
      GIN_MODE: release
      LOG_LEVEL: info
    
  # 订单服务
  orderService:
    enabled: true
    name: order-service
    image:
      repository: order-service
      tag: "1.0.0"
      pullPolicy: IfNotPresent
    replicaCount: 3
    port: 3003
    resources:
      requests:
        cpu: 200m
        memory: 256Mi
      limits:
        cpu: 1000m
        memory: 1Gi
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 15
      targetCPUUtilizationPercentage: 70
    env:
      SPRING_PROFILES_ACTIVE: production
      JAVA_OPTS: "-Xmx512m -Xms256m"
    
  # 支付服务
  paymentService:
    enabled: true
    name: payment-service
    image:
      repository: payment-service
      tag: "1.0.0"
      pullPolicy: IfNotPresent
    replicaCount: 2
    port: 3004
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 8
      targetCPUUtilizationPercentage: 70
    env:
      FLASK_ENV: production
      LOG_LEVEL: info

# 前端应用配置
frontend:
  # Web应用
  webApp:
    enabled: true
    name: web-app
    image:
      repository: web-app
      tag: "1.0.0"
      pullPolicy: IfNotPresent
    replicaCount: 3
    port: 80
    resources:
      requests:
        cpu: 50m
        memory: 64Mi
      limits:
        cpu: 200m
        memory: 256Mi
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
    
  # 管理后台
  adminPanel:
    enabled: true
    name: admin-panel
    image:
      repository: admin-panel
      tag: "1.0.0"
      pullPolicy: IfNotPresent
    replicaCount: 2
    port: 80
    resources:
      requests:
        cpu: 50m
        memory: 64Mi
      limits:
        cpu: 200m
        memory: 256Mi

# 数据库配置
postgresql:
  enabled: true
  auth:
    postgresPassword: "secure-postgres-password"
    database: "ecommerce"
  primary:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: "fast-ssd"
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1000m
        memory: 2Gi
  readReplicas:
    replicaCount: 2
    persistence:
      enabled: true
      size: 20Gi
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 500m
        memory: 1Gi

# Redis配置
redis:
  enabled: true
  auth:
    enabled: true
    password: "secure-redis-password"
  master:
    persistence:
      enabled: true
      size: 8Gi
      storageClass: "fast-ssd"
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
  replica:
    replicaCount: 2
    persistence:
      enabled: true
      size: 8Gi
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 250m
        memory: 256Mi

# 消息队列配置
rabbitmq:
  enabled: true
  auth:
    username: "admin"
    password: "secure-rabbitmq-password"
  persistence:
    enabled: true
    size: 8Gi
    storageClass: "fast-ssd"
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi
  clustering:
    enabled: true
    replicaCount: 3

# 搜索引擎配置
elasticsearch:
  enabled: true
  master:
    replicaCount: 3
    persistence:
      enabled: true
      size: 10Gi
      storageClass: "fast-ssd"
    resources:
      requests:
        cpu: 250m
        memory: 1Gi
      limits:
        cpu: 1000m
        memory: 2Gi
  data:
    replicaCount: 3
    persistence:
      enabled: true
      size: 50Gi
    resources:
      requests:
        cpu: 500m
        memory: 2Gi
      limits:
        cpu: 2000m
        memory: 4Gi

# 网关配置
gateway:
  enabled: true
  type: "nginx" # nginx, istio, ambassador
  nginx:
    replicaCount: 3
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10

# 服务网格配置
serviceMesh:
  istio:
    enabled: false
    injection: true
    gateways:
      enabled: true
    virtualServices:
      enabled: true
    destinationRules:
      enabled: true

# 监控配置
monitoring:
  prometheus:
    enabled: true
    retention: "30d"
    storage:
      size: 50Gi
      storageClass: "fast-ssd"
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2000m
        memory: 4Gi
  
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
    
  alertmanager:
    enabled: true
    persistence:
      enabled: true
      size: 2Gi

# 分布式追踪配置
tracing:
  jaeger:
    enabled: true
    storage:
      type: "elasticsearch"
    collector:
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 500m
          memory: 512Mi
    query:
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 500m
          memory: 512Mi

# 日志配置
logging:
  elasticsearch:
    enabled: true
  logstash:
    enabled: true
    resources:
      requests:
        cpu: 200m
        memory: 512Mi
      limits:
        cpu: 1000m
        memory: 1Gi
  kibana:
    enabled: true
    resources:
      requests:
        cpu: 100m
        memory: 256Mi
      limits:
        cpu: 500m
        memory: 1Gi

# 安全配置
security:
  vault:
    enabled: true
    replicaCount: 3
    storage:
      size: 10Gi
      storageClass: "fast-ssd"
  
  falco:
    enabled: true
    resources:
      requests:
        cpu: 100m
        memory: 512Mi
      limits:
        cpu: 1000m
        memory: 1Gi
  
  opa:
    enabled: true
    gatekeeper:
      enabled: true

# 网络策略
networkPolicies:
  enabled: true
  defaultDeny: true
  
# 入口配置
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: ecommerce.example.com
      paths:
        - path: /
          pathType: Prefix
          service: web-app
        - path: /api
          pathType: Prefix
          service: api-gateway
    - host: admin.ecommerce.example.com
      paths:
        - path: /
          pathType: Prefix
          service: admin-panel
  tls:
    - secretName: ecommerce-tls
      hosts:
        - ecommerce.example.com
        - admin.ecommerce.example.com
