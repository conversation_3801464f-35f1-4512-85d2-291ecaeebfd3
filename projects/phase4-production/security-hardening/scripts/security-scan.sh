#!/bin/bash

# 综合安全扫描脚本
# 执行容器、Kubernetes和网络安全检查

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
NAMESPACE="${1:-production}"
REPORT_DIR="security-reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
REPORT_FILE="${REPORT_DIR}/security_scan_${TIMESTAMP}.json"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建报告目录
create_report_dir() {
    mkdir -p "$REPORT_DIR"
    log_info "创建报告目录: $REPORT_DIR"
}

# 检查依赖工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    local tools=("kubectl" "trivy" "docker" "jq")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少以下工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具后重试"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 扫描容器镜像
scan_container_images() {
    log_info "开始扫描容器镜像..."
    
    local images
    images=$(kubectl get pods -n "$NAMESPACE" -o jsonpath='{.items[*].spec.containers[*].image}' | tr ' ' '\n' | sort -u)
    
    local scan_results=()
    
    while IFS= read -r image; do
        if [ -n "$image" ]; then
            log_info "扫描镜像: $image"
            
            local image_report="${REPORT_DIR}/image_$(echo "$image" | tr '/:' '_')_${TIMESTAMP}.json"
            
            if trivy image --format json --output "$image_report" --severity HIGH,CRITICAL "$image" 2>/dev/null; then
                local vulnerabilities
                vulnerabilities=$(jq '.Results[]?.Vulnerabilities // [] | length' "$image_report" 2>/dev/null || echo "0")
                
                scan_results+=("{\"image\":\"$image\",\"vulnerabilities\":$vulnerabilities,\"report\":\"$image_report\"}")
                
                if [ "$vulnerabilities" -gt 0 ]; then
                    log_warning "镜像 $image 发现 $vulnerabilities 个高危漏洞"
                else
                    log_success "镜像 $image 未发现高危漏洞"
                fi
            else
                log_error "扫描镜像 $image 失败"
                scan_results+=("{\"image\":\"$image\",\"vulnerabilities\":-1,\"error\":\"scan_failed\"}")
            fi
        fi
    done <<< "$images"
    
    # 生成镜像扫描汇总
    local image_summary
    image_summary=$(printf '%s\n' "${scan_results[@]}" | jq -s '.')
    echo "$image_summary" > "${REPORT_DIR}/image_scan_summary_${TIMESTAMP}.json"
    
    log_success "容器镜像扫描完成"
}

# 检查Pod安全策略
check_pod_security() {
    log_info "检查Pod安全策略..."
    
    local pods
    pods=$(kubectl get pods -n "$NAMESPACE" -o json)
    
    local security_issues=()
    
    # 检查运行用户
    local root_pods
    root_pods=$(echo "$pods" | jq -r '.items[] | select(.spec.securityContext.runAsUser == 0 or (.spec.containers[]?.securityContext.runAsUser // 0) == 0) | .metadata.name')
    
    if [ -n "$root_pods" ]; then
        while IFS= read -r pod; do
            if [ -n "$pod" ]; then
                security_issues+=("{\"type\":\"root_user\",\"pod\":\"$pod\",\"severity\":\"HIGH\"}")
                log_warning "Pod $pod 以root用户运行"
            fi
        done <<< "$root_pods"
    fi
    
    # 检查特权容器
    local privileged_pods
    privileged_pods=$(echo "$pods" | jq -r '.items[] | select(.spec.containers[]?.securityContext.privileged == true) | .metadata.name')
    
    if [ -n "$privileged_pods" ]; then
        while IFS= read -r pod; do
            if [ -n "$pod" ]; then
                security_issues+=("{\"type\":\"privileged_container\",\"pod\":\"$pod\",\"severity\":\"CRITICAL\"}")
                log_error "Pod $pod 运行特权容器"
            fi
        done <<< "$privileged_pods"
    fi
    
    # 检查资源限制
    local no_limits_pods
    no_limits_pods=$(echo "$pods" | jq -r '.items[] | select(.spec.containers[] | has("resources") | not or (.resources | has("limits") | not)) | .metadata.name')
    
    if [ -n "$no_limits_pods" ]; then
        while IFS= read -r pod; do
            if [ -n "$pod" ]; then
                security_issues+=("{\"type\":\"no_resource_limits\",\"pod\":\"$pod\",\"severity\":\"MEDIUM\"}")
                log_warning "Pod $pod 未设置资源限制"
            fi
        done <<< "$no_limits_pods"
    fi
    
    # 生成Pod安全检查报告
    local pod_security_summary
    pod_security_summary=$(printf '%s\n' "${security_issues[@]}" | jq -s '.')
    echo "$pod_security_summary" > "${REPORT_DIR}/pod_security_${TIMESTAMP}.json"
    
    log_success "Pod安全策略检查完成"
}

# 检查网络策略
check_network_policies() {
    log_info "检查网络策略..."
    
    local network_policies
    network_policies=$(kubectl get networkpolicy -n "$NAMESPACE" -o json 2>/dev/null || echo '{"items":[]}')
    
    local policy_count
    policy_count=$(echo "$network_policies" | jq '.items | length')
    
    local network_issues=()
    
    if [ "$policy_count" -eq 0 ]; then
        network_issues+=("{\"type\":\"no_network_policies\",\"namespace\":\"$NAMESPACE\",\"severity\":\"HIGH\"}")
        log_warning "命名空间 $NAMESPACE 未配置网络策略"
    else
        log_success "命名空间 $NAMESPACE 已配置 $policy_count 个网络策略"
    fi
    
    # 检查默认拒绝策略
    local default_deny
    default_deny=$(echo "$network_policies" | jq -r '.items[] | select(.spec.podSelector == {} and (.spec.policyTypes[]? == "Ingress" or .spec.policyTypes[]? == "Egress")) | .metadata.name')
    
    if [ -z "$default_deny" ]; then
        network_issues+=("{\"type\":\"no_default_deny\",\"namespace\":\"$NAMESPACE\",\"severity\":\"MEDIUM\"}")
        log_warning "命名空间 $NAMESPACE 未配置默认拒绝策略"
    else
        log_success "命名空间 $NAMESPACE 已配置默认拒绝策略"
    fi
    
    # 生成网络策略检查报告
    local network_summary
    network_summary=$(printf '%s\n' "${network_issues[@]}" | jq -s '.')
    echo "$network_summary" > "${REPORT_DIR}/network_policy_${TIMESTAMP}.json"
    
    log_success "网络策略检查完成"
}

# 检查RBAC配置
check_rbac() {
    log_info "检查RBAC配置..."
    
    local rbac_issues=()
    
    # 检查过度权限的ClusterRoleBinding
    local cluster_admin_bindings
    cluster_admin_bindings=$(kubectl get clusterrolebinding -o json | jq -r '.items[] | select(.roleRef.name == "cluster-admin") | .metadata.name')
    
    if [ -n "$cluster_admin_bindings" ]; then
        while IFS= read -r binding; do
            if [ -n "$binding" ]; then
                rbac_issues+=("{\"type\":\"cluster_admin_binding\",\"binding\":\"$binding\",\"severity\":\"HIGH\"}")
                log_warning "发现cluster-admin绑定: $binding"
            fi
        done <<< "$cluster_admin_bindings"
    fi
    
    # 检查默认服务账户的权限
    local default_sa_bindings
    default_sa_bindings=$(kubectl get rolebinding,clusterrolebinding -A -o json | jq -r '.items[] | select(.subjects[]?.name == "default") | "\(.metadata.namespace // "cluster"):\(.metadata.name)"')
    
    if [ -n "$default_sa_bindings" ]; then
        while IFS= read -r binding; do
            if [ -n "$binding" ]; then
                rbac_issues+=("{\"type\":\"default_sa_binding\",\"binding\":\"$binding\",\"severity\":\"MEDIUM\"}")
                log_warning "默认服务账户绑定: $binding"
            fi
        done <<< "$default_sa_bindings"
    fi
    
    # 生成RBAC检查报告
    local rbac_summary
    rbac_summary=$(printf '%s\n' "${rbac_issues[@]}" | jq -s '.')
    echo "$rbac_summary" > "${REPORT_DIR}/rbac_check_${TIMESTAMP}.json"
    
    log_success "RBAC配置检查完成"
}

# 检查密钥管理
check_secrets() {
    log_info "检查密钥管理..."
    
    local secrets
    secrets=$(kubectl get secrets -n "$NAMESPACE" -o json)
    
    local secret_issues=()
    
    # 检查未加密的密钥
    local unencrypted_secrets
    unencrypted_secrets=$(echo "$secrets" | jq -r '.items[] | select(.type != "kubernetes.io/service-account-token" and .type != "kubernetes.io/dockercfg" and .type != "kubernetes.io/dockerconfigjson") | .metadata.name')
    
    if [ -n "$unencrypted_secrets" ]; then
        while IFS= read -r secret; do
            if [ -n "$secret" ]; then
                # 检查是否使用了外部密钥管理
                local external_managed
                external_managed=$(kubectl get secret "$secret" -n "$NAMESPACE" -o json | jq -r '.metadata.annotations["vault.hashicorp.com/agent-inject"] // .metadata.annotations["external-secrets.io/backend"] // "false"')
                
                if [ "$external_managed" = "false" ]; then
                    secret_issues+=("{\"type\":\"unmanaged_secret\",\"secret\":\"$secret\",\"severity\":\"MEDIUM\"}")
                    log_warning "密钥 $secret 未使用外部密钥管理"
                fi
            fi
        done <<< "$unencrypted_secrets"
    fi
    
    # 生成密钥检查报告
    local secrets_summary
    secrets_summary=$(printf '%s\n' "${secret_issues[@]}" | jq -s '.')
    echo "$secrets_summary" > "${REPORT_DIR}/secrets_check_${TIMESTAMP}.json"
    
    log_success "密钥管理检查完成"
}

# 生成综合报告
generate_summary_report() {
    log_info "生成综合安全报告..."
    
    local summary_report="{
        \"timestamp\": \"$TIMESTAMP\",
        \"namespace\": \"$NAMESPACE\",
        \"scan_type\": \"comprehensive_security_scan\",
        \"reports\": {
            \"image_scan\": \"${REPORT_DIR}/image_scan_summary_${TIMESTAMP}.json\",
            \"pod_security\": \"${REPORT_DIR}/pod_security_${TIMESTAMP}.json\",
            \"network_policy\": \"${REPORT_DIR}/network_policy_${TIMESTAMP}.json\",
            \"rbac_check\": \"${REPORT_DIR}/rbac_check_${TIMESTAMP}.json\",
            \"secrets_check\": \"${REPORT_DIR}/secrets_check_${TIMESTAMP}.json\"
        }
    }"
    
    echo "$summary_report" | jq '.' > "$REPORT_FILE"
    
    log_success "综合安全报告生成完成: $REPORT_FILE"
}

# 显示扫描结果摘要
show_scan_summary() {
    log_info "安全扫描结果摘要:"
    echo ""
    
    # 统计各类问题数量
    local total_issues=0
    local critical_issues=0
    local high_issues=0
    local medium_issues=0
    
    for report in "${REPORT_DIR}"/*_"${TIMESTAMP}".json; do
        if [ -f "$report" ]; then
            local issues
            issues=$(jq '. | length' "$report" 2>/dev/null || echo "0")
            total_issues=$((total_issues + issues))
            
            local critical
            critical=$(jq '[.[] | select(.severity == "CRITICAL")] | length' "$report" 2>/dev/null || echo "0")
            critical_issues=$((critical_issues + critical))
            
            local high
            high=$(jq '[.[] | select(.severity == "HIGH")] | length' "$report" 2>/dev/null || echo "0")
            high_issues=$((high_issues + high))
            
            local medium
            medium=$(jq '[.[] | select(.severity == "MEDIUM")] | length' "$report" 2>/dev/null || echo "0")
            medium_issues=$((medium_issues + medium))
        fi
    done
    
    echo "总问题数: $total_issues"
    echo "严重问题: $critical_issues"
    echo "高危问题: $high_issues"
    echo "中危问题: $medium_issues"
    echo ""
    echo "详细报告位置: $REPORT_DIR"
    echo "综合报告: $REPORT_FILE"
}

# 主函数
main() {
    log_info "开始执行安全扫描 (命名空间: $NAMESPACE)..."
    
    create_report_dir
    check_dependencies
    scan_container_images
    check_pod_security
    check_network_policies
    check_rbac
    check_secrets
    generate_summary_report
    show_scan_summary
    
    log_success "安全扫描完成！"
}

# 执行主函数
main "$@"
