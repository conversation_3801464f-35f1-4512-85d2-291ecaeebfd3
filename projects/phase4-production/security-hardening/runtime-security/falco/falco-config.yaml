# Falco运行时安全监控配置
# 实施容器和Kubernetes运行时威胁检测

---
# Falco命名空间
apiVersion: v1
kind: Namespace
metadata:
  name: falco
  labels:
    name: falco
    security-monitoring: enabled

---
# Falco服务账户
apiVersion: v1
kind: ServiceAccount
metadata:
  name: falco
  namespace: falco
automountServiceAccountToken: true

---
# Falco ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: falco
rules:
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["pods", "nodes"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  verbs: ["get", "list", "watch"]

---
# Falco ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: falco
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: falco
subjects:
- kind: ServiceAccount
  name: falco
  namespace: falco

---
# Falco配置ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-config
  namespace: falco
data:
  falco.yaml: |
    # Falco主配置文件
    
    # 规则文件
    rules_file:
      - /etc/falco/falco_rules.yaml
      - /etc/falco/falco_rules.local.yaml
      - /etc/falco/k8s_audit_rules.yaml
      - /etc/falco/rules.d
    
    # 时间格式
    time_format_iso_8601: false
    
    # JSON输出
    json_output: true
    json_include_output_property: true
    json_include_tags_property: true
    
    # 日志级别
    log_level: info
    log_stderr: true
    log_syslog: false
    
    # 优先级
    priority: debug
    
    # 缓冲输出
    buffered_outputs: false
    
    # 输出配置
    outputs:
      rate: 1
      max_burst: 1000
    
    # 系统调用事件源
    syscall_event_drops:
      actions:
        - log
        - alert
      rate: 0.03333
      max_burst: 10
    
    # 文件输出
    file_output:
      enabled: true
      keep_alive: false
      filename: /var/log/falco/events.log
    
    # 标准输出
    stdout_output:
      enabled: true
    
    # Syslog输出
    syslog_output:
      enabled: false
    
    # 程序输出
    program_output:
      enabled: false
      keep_alive: false
      program: "jq '{text: .output}' | curl -d @- -X POST https://hooks.slack.com/services/XXX"
    
    # HTTP输出
    http_output:
      enabled: false
      url: "http://some.url/some/path/"
      user_agent: "falcosecurity/falco"
    
    # gRPC输出
    grpc_output:
      enabled: false
      address: "unix:///var/run/falco/falco.sock"
      threadiness: 0
    
    # gRPC服务器
    grpc:
      enabled: false
      bind_address: "0.0.0.0:5060"
      threadiness: 0
    
    # Webserver
    webserver:
      enabled: true
      listen_port: 8765
      k8s_healthz_endpoint: /healthz
      ssl_enabled: false
      ssl_certificate: /etc/ssl/falco/falco.pem
    
    # 监控指标
    metrics:
      enabled: true
      interval: 1h
      output_rule: true
      rules_counters_enabled: true
      resource_utilization_enabled: true
      state_counters_enabled: true
      kernel_event_counters_enabled: true
      libbpf_stats_enabled: true
      convert_memory_to_mb: true
      include_empty_values: false
    
  falco_rules.local.yaml: |
    # 自定义Falco规则
    
    # 检测特权容器启动
    - rule: Privileged Container Started
      desc: 检测特权容器启动
      condition: >
        spawned_process and container and
        k8s_audit and ka.verb=create and ka.target.resource=pods and
        ka.req.pod.spec.containers.securityContext.privileged=true
      output: >
        特权容器启动 (user=%ka.user.name verb=%ka.verb 
        pod=%ka.target.name container=%ka.req.pod.spec.containers.name 
        image=%ka.req.pod.spec.containers.image)
      priority: WARNING
      tags: [container, privilege_escalation, k8s]
    
    # 检测root用户执行
    - rule: Root User Execution
      desc: 检测容器中root用户执行命令
      condition: >
        spawned_process and container and
        proc.pname exists and user.uid=0
      output: >
        Root用户执行命令 (user=%user.name command=%proc.cmdline 
        container=%container.name image=%container.image.repository)
      priority: WARNING
      tags: [container, privilege_escalation]
    
    # 检测敏感文件访问
    - rule: Sensitive File Access
      desc: 检测对敏感文件的访问
      condition: >
        open_read and container and
        (fd.name startswith /etc/passwd or
         fd.name startswith /etc/shadow or
         fd.name startswith /etc/ssh/ or
         fd.name startswith /root/.ssh/)
      output: >
        敏感文件访问 (user=%user.name command=%proc.cmdline 
        file=%fd.name container=%container.name)
      priority: HIGH
      tags: [filesystem, sensitive_files]
    
    # 检测网络连接
    - rule: Unexpected Outbound Connection
      desc: 检测意外的出站网络连接
      condition: >
        outbound and container and
        not fd.sport in (80, 443, 53, 22) and
        not proc.name in (curl, wget, apt, yum, pip)
      output: >
        意外出站连接 (user=%user.name command=%proc.cmdline 
        connection=%fd.name container=%container.name)
      priority: NOTICE
      tags: [network, outbound]
    
    # 检测包管理器执行
    - rule: Package Manager Execution
      desc: 检测容器中包管理器的执行
      condition: >
        spawned_process and container and
        proc.name in (apt, apt-get, yum, rpm, pip, npm, gem)
      output: >
        包管理器执行 (user=%user.name command=%proc.cmdline 
        container=%container.name image=%container.image.repository)
      priority: WARNING
      tags: [container, package_management]
    
    # 检测Shell执行
    - rule: Shell Execution in Container
      desc: 检测容器中Shell的执行
      condition: >
        spawned_process and container and
        proc.name in (bash, sh, zsh, fish, csh, tcsh)
      output: >
        容器中Shell执行 (user=%user.name command=%proc.cmdline 
        container=%container.name image=%container.image.repository)
      priority: NOTICE
      tags: [container, shell]
    
    # 检测文件系统修改
    - rule: Write to Non-Writable Directory
      desc: 检测对只读目录的写入
      condition: >
        open_write and container and
        (fd.name startswith /usr or
         fd.name startswith /bin or
         fd.name startswith /sbin or
         fd.name startswith /boot)
      output: >
        只读目录写入 (user=%user.name command=%proc.cmdline 
        file=%fd.name container=%container.name)
      priority: HIGH
      tags: [filesystem, write_protection]

---
# Falco DaemonSet
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: falco
  namespace: falco
  labels:
    app: falco
spec:
  selector:
    matchLabels:
      app: falco
  template:
    metadata:
      labels:
        app: falco
    spec:
      serviceAccountName: falco
      hostNetwork: true
      hostPID: true
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
      - effect: NoSchedule
        key: node-role.kubernetes.io/control-plane
      containers:
      - name: falco
        image: falcosecurity/falco-no-driver:0.36.2
        imagePullPolicy: IfNotPresent
        securityContext:
          privileged: true
        args:
        - /usr/bin/falco
        - --cri=/run/containerd/containerd.sock
        - --cri=/run/crio/crio.sock
        - -K=/var/run/secrets/kubernetes.io/serviceaccount/token
        - -k=https://kubernetes.default
        - --k8s-node=$(FALCO_K8S_NODE_NAME)
        - -pk
        env:
        - name: FALCO_K8S_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: FALCO_GRPC_BIND_ADDRESS
          value: "unix:///var/run/falco/falco.sock"
        - name: FALCO_GRPC_ENABLED
          value: "true"
        ports:
        - containerPort: 8765
          name: metrics
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 512Mi
        volumeMounts:
        - mountPath: /etc/falco
          name: falco-config
        - mountPath: /var/run/falco
          name: falco-socket-dir
        - mountPath: /host/var/run/docker.sock
          name: docker-socket
          readOnly: true
        - mountPath: /host/run/containerd/containerd.sock
          name: containerd-socket
          readOnly: true
        - mountPath: /host/dev
          name: dev-fs
          readOnly: true
        - mountPath: /host/proc
          name: proc-fs
          readOnly: true
        - mountPath: /host/boot
          name: boot-fs
          readOnly: true
        - mountPath: /host/lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host/usr
          name: usr-fs
          readOnly: true
        - mountPath: /host/etc
          name: etc-fs
          readOnly: true
        - mountPath: /var/log/falco
          name: falco-logs
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8765
          initialDelaySeconds: 60
          periodSeconds: 15
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8765
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 5
      volumes:
      - name: falco-config
        configMap:
          name: falco-config
      - name: falco-socket-dir
        emptyDir: {}
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
      - name: containerd-socket
        hostPath:
          path: /run/containerd/containerd.sock
      - name: dev-fs
        hostPath:
          path: /dev
      - name: proc-fs
        hostPath:
          path: /proc
      - name: boot-fs
        hostPath:
          path: /boot
      - name: lib-modules
        hostPath:
          path: /lib/modules
      - name: usr-fs
        hostPath:
          path: /usr
      - name: etc-fs
        hostPath:
          path: /etc
      - name: falco-logs
        hostPath:
          path: /var/log/falco
          type: DirectoryOrCreate

---
# Falco Service
apiVersion: v1
kind: Service
metadata:
  name: falco
  namespace: falco
  labels:
    app: falco
spec:
  type: ClusterIP
  ports:
  - name: metrics
    port: 8765
    targetPort: 8765
    protocol: TCP
  selector:
    app: falco
