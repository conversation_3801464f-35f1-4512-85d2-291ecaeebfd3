# HashiCorp Vault集成配置
# 实施企业级密钥管理和安全存储

---
# Vault命名空间
apiVersion: v1
kind: Namespace
metadata:
  name: vault
  labels:
    name: vault
    security-level: high

---
# Vault服务账户
apiVersion: v1
kind: ServiceAccount
metadata:
  name: vault
  namespace: vault
automountServiceAccountToken: true

---
# Vault ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: vault-auth
rules:
- apiGroups: [""]
  resources: ["serviceaccounts"]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["serviceaccounts/token"]
  verbs: ["create"]

---
# Vault ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: vault-auth
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: vault-auth
subjects:
- kind: ServiceAccount
  name: vault
  namespace: vault

---
# Vault配置ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: vault-config
  namespace: vault
data:
  vault.hcl: |
    ui = true
    
    listener "tcp" {
      address = "0.0.0.0:8200"
      tls_disable = false
      tls_cert_file = "/vault/tls/tls.crt"
      tls_key_file = "/vault/tls/tls.key"
    }
    
    storage "consul" {
      address = "consul:8500"
      path = "vault/"
    }
    
    # 或者使用文件存储（开发环境）
    # storage "file" {
    #   path = "/vault/data"
    # }
    
    # API地址
    api_addr = "https://vault.vault.svc.cluster.local:8200"
    cluster_addr = "https://vault.vault.svc.cluster.local:8201"
    
    # 日志级别
    log_level = "INFO"
    
    # 禁用mlock（在容器中）
    disable_mlock = true

---
# Vault TLS证书Secret
apiVersion: v1
kind: Secret
metadata:
  name: vault-tls
  namespace: vault
type: kubernetes.io/tls
data:
  # 这里应该包含实际的TLS证书和私钥
  tls.crt: LS0tLS1CRUdJTi... # 证书内容
  tls.key: LS0tLS1CRUdJTi... # 私钥内容

---
# Vault StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vault
  namespace: vault
  labels:
    app: vault
spec:
  serviceName: vault
  replicas: 3
  selector:
    matchLabels:
      app: vault
  template:
    metadata:
      labels:
        app: vault
    spec:
      serviceAccountName: vault
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: vault
        image: hashicorp/vault:1.15.2
        imagePullPolicy: IfNotPresent
        command:
        - vault
        - server
        - -config=/vault/config/vault.hcl
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
            add:
            - IPC_LOCK
        env:
        - name: VAULT_ADDR
          value: "https://127.0.0.1:8200"
        - name: VAULT_API_ADDR
          value: "https://vault.vault.svc.cluster.local:8200"
        - name: VAULT_CLUSTER_ADDR
          value: "https://$(POD_IP):8201"
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: VAULT_LOG_LEVEL
          value: "info"
        ports:
        - containerPort: 8200
          name: vault-port
          protocol: TCP
        - containerPort: 8201
          name: cluster-port
          protocol: TCP
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: vault-config
          mountPath: /vault/config
        - name: vault-tls
          mountPath: /vault/tls
        - name: vault-data
          mountPath: /vault/data
        - name: tmp
          mountPath: /tmp
        livenessProbe:
          httpGet:
            path: /v1/sys/health?standbyok=true
            port: 8200
            scheme: HTTPS
          initialDelaySeconds: 60
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /v1/sys/health?standbyok=true&sealedcode=204&uninitcode=204
            port: 8200
            scheme: HTTPS
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: vault-config
        configMap:
          name: vault-config
      - name: vault-tls
        secret:
          secretName: vault-tls
      - name: tmp
        emptyDir: {}
  volumeClaimTemplates:
  - metadata:
      name: vault-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi

---
# Vault Service
apiVersion: v1
kind: Service
metadata:
  name: vault
  namespace: vault
  labels:
    app: vault
spec:
  type: ClusterIP
  ports:
  - name: vault-port
    port: 8200
    targetPort: 8200
    protocol: TCP
  - name: cluster-port
    port: 8201
    targetPort: 8201
    protocol: TCP
  selector:
    app: vault

---
# Vault Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: vault-ingress
  namespace: vault
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - vault.internal.example.com
    secretName: vault-ingress-tls
  rules:
  - host: vault.internal.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: vault
            port:
              number: 8200

---
# Vault初始化Job
apiVersion: batch/v1
kind: Job
metadata:
  name: vault-init
  namespace: vault
spec:
  template:
    metadata:
      labels:
        app: vault-init
    spec:
      serviceAccountName: vault
      restartPolicy: OnFailure
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: vault-init
        image: hashicorp/vault:1.15.2
        command:
        - /bin/sh
        - -c
        - |
          # 等待Vault服务启动
          until vault status; do
            echo "等待Vault启动..."
            sleep 5
          done
          
          # 检查是否已初始化
          if vault status | grep -q "Initialized.*true"; then
            echo "Vault已经初始化"
            exit 0
          fi
          
          # 初始化Vault
          echo "初始化Vault..."
          vault operator init -key-shares=5 -key-threshold=3 -format=json > /tmp/vault-init.json
          
          # 保存初始化信息到Secret
          kubectl create secret generic vault-init \
            --from-file=/tmp/vault-init.json \
            --namespace=vault || true
          
          echo "Vault初始化完成"
        env:
        - name: VAULT_ADDR
          value: "https://vault.vault.svc.cluster.local:8200"
        - name: VAULT_SKIP_VERIFY
          value: "true"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
