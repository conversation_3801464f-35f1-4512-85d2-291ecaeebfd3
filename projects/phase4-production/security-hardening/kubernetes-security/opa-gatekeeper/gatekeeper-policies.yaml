# OPA Gatekeeper策略配置
# 实施Kubernetes准入控制和策略执行

---
# 安装Gatekeeper系统
apiVersion: v1
kind: Namespace
metadata:
  name: gatekeeper-system
  labels:
    admission.gatekeeper.sh/ignore: "true"

---
# 必须有标签的约束模板
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8srequiredlabels
spec:
  crd:
    spec:
      names:
        kind: K8sRequiredLabels
      validation:
        type: object
        properties:
          labels:
            type: array
            items:
              type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8srequiredlabels

        violation[{"msg": msg}] {
          required := input.parameters.labels
          provided := input.review.object.metadata.labels
          missing := required[_]
          not provided[missing]
          msg := sprintf("缺少必需的标签: %v", [missing])
        }

---
# 应用必须有标签的约束
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequiredLabels
metadata:
  name: must-have-app-label
spec:
  match:
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment", "StatefulSet", "DaemonSet"]
      - apiGroups: [""]
        kinds: ["Pod"]
  parameters:
    labels: ["app", "version", "environment"]

---
# 禁止特权容器的约束模板
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8spsprivileged
spec:
  crd:
    spec:
      names:
        kind: K8sPSPrivileged
      validation:
        type: object
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8spsprivileged

        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          container.securityContext.privileged
          msg := "特权容器是不被允许的"
        }

        violation[{"msg": msg}] {
          container := input.review.object.spec.initContainers[_]
          container.securityContext.privileged
          msg := "特权初始化容器是不被允许的"
        }

---
# 禁止特权容器的约束
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sPSPrivileged
metadata:
  name: psp-privileged
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
  excludedNamespaces: ["kube-system", "gatekeeper-system"]

---
# 要求非root用户的约束模板
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8spsrunasnonroot
spec:
  crd:
    spec:
      names:
        kind: K8sPSRunAsNonRoot
      validation:
        type: object
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8spsrunasnonroot

        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          not container.securityContext.runAsNonRoot
          msg := "容器必须以非root用户运行"
        }

        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          container.securityContext.runAsUser == 0
          msg := "容器不能以UID 0 (root)运行"
        }

---
# 要求非root用户的约束
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sPSRunAsNonRoot
metadata:
  name: psp-run-as-non-root
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
  excludedNamespaces: ["kube-system", "gatekeeper-system"]

---
# 禁止hostNetwork的约束模板
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8spshostnetwork
spec:
  crd:
    spec:
      names:
        kind: K8sPSHostNetwork
      validation:
        type: object
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8spshostnetwork

        violation[{"msg": msg}] {
          input.review.object.spec.hostNetwork
          msg := "使用主机网络是不被允许的"
        }

---
# 禁止hostNetwork的约束
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sPSHostNetwork
metadata:
  name: psp-host-network
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
  excludedNamespaces: ["kube-system", "gatekeeper-system"]

---
# 要求资源限制的约束模板
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8srequiredresources
spec:
  crd:
    spec:
      names:
        kind: K8sRequiredResources
      validation:
        type: object
        properties:
          limits:
            type: array
            items:
              type: string
          requests:
            type: array
            items:
              type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8srequiredresources

        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          required_limits := input.parameters.limits
          limit_type := required_limits[_]
          not container.resources.limits[limit_type]
          msg := sprintf("容器缺少资源限制: %v", [limit_type])
        }

        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          required_requests := input.parameters.requests
          request_type := required_requests[_]
          not container.resources.requests[request_type]
          msg := sprintf("容器缺少资源请求: %v", [request_type])
        }

---
# 要求资源限制的约束
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequiredResources
metadata:
  name: must-have-resources
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
  excludedNamespaces: ["kube-system", "gatekeeper-system"]
  parameters:
    limits: ["memory", "cpu"]
    requests: ["memory", "cpu"]

---
# 禁止latest标签的约束模板
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8sdisallowedtags
spec:
  crd:
    spec:
      names:
        kind: K8sDisallowedTags
      validation:
        type: object
        properties:
          tags:
            type: array
            items:
              type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8sdisallowedtags

        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          tag := get_tag(container.image)
          tag == input.parameters.tags[_]
          msg := sprintf("禁止使用镜像标签: %v", [tag])
        }

        get_tag(image) = tag {
          contains(image, ":")
          tag := split(image, ":")[1]
        }

        get_tag(image) = "latest" {
          not contains(image, ":")
        }

---
# 禁止latest标签的约束
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sDisallowedTags
metadata:
  name: disallow-latest-tag
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
      - apiGroups: ["apps"]
        kinds: ["Deployment", "StatefulSet", "DaemonSet"]
  excludedNamespaces: ["kube-system", "gatekeeper-system"]
  parameters:
    tags: ["latest", "master", "main"]

---
# 配置Gatekeeper系统配置
apiVersion: config.gatekeeper.sh/v1alpha1
kind: Config
metadata:
  name: config
  namespace: gatekeeper-system
spec:
  match:
    - excludedNamespaces: ["kube-system", "gatekeeper-system"]
      processes: ["*"]
  validation:
    traces:
      - user:
          kind:
            group: "*"
            version: "*"
            kind: "*"
  readiness:
    statsEnabled: true
