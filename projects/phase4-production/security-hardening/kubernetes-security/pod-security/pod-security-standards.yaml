# Pod Security Standards配置
# 实施Kubernetes Pod安全策略

---
# 创建受限命名空间（最高安全级别）
apiVersion: v1
kind: Namespace
metadata:
  name: restricted-ns
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
    security-level: high

---
# 创建基线命名空间（中等安全级别）
apiVersion: v1
kind: Namespace
metadata:
  name: baseline-ns
  labels:
    pod-security.kubernetes.io/enforce: baseline
    pod-security.kubernetes.io/audit: baseline
    pod-security.kubernetes.io/warn: baseline
    security-level: medium

---
# 创建特权命名空间（低安全级别，仅用于系统组件）
apiVersion: v1
kind: Namespace
metadata:
  name: privileged-ns
  labels:
    pod-security.kubernetes.io/enforce: privileged
    pod-security.kubernetes.io/audit: privileged
    pod-security.kubernetes.io/warn: privileged
    security-level: low

---
# 安全的Pod示例（符合restricted标准）
apiVersion: v1
kind: Pod
metadata:
  name: secure-pod
  namespace: restricted-ns
  labels:
    app: secure-app
spec:
  serviceAccountName: secure-service-account
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 3000
    fsGroup: 2000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: secure-container
    image: nginx:1.21-alpine
    ports:
    - containerPort: 8080
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      runAsNonRoot: true
      runAsUser: 1000
      capabilities:
        drop:
        - ALL
      seccompProfile:
        type: RuntimeDefault
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
    - name: cache-volume
      mountPath: /var/cache/nginx
    livenessProbe:
      httpGet:
        path: /
        port: 8080
      initialDelaySeconds: 30
      periodSeconds: 10
    readinessProbe:
      httpGet:
        path: /
        port: 8080
      initialDelaySeconds: 5
      periodSeconds: 5
  volumes:
  - name: tmp-volume
    emptyDir: {}
  - name: cache-volume
    emptyDir: {}

---
# 不安全的Pod示例（违反安全策略）
apiVersion: v1
kind: Pod
metadata:
  name: insecure-pod-example
  namespace: baseline-ns
  annotations:
    description: "此Pod展示了不安全的配置，仅用于教学目的"
spec:
  containers:
  - name: insecure-container
    image: nginx:latest  # 使用latest标签
    securityContext:
      privileged: true   # 特权模式
      runAsUser: 0      # root用户
      allowPrivilegeEscalation: true
    volumeMounts:
    - name: host-volume
      mountPath: /host
  volumes:
  - name: host-volume
    hostPath:
      path: /
      type: Directory

---
# Pod安全策略验证Job
apiVersion: batch/v1
kind: Job
metadata:
  name: security-validation
  namespace: restricted-ns
spec:
  template:
    metadata:
      labels:
        app: security-validator
    spec:
      serviceAccountName: security-validator
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: validator
        image: alpine:3.18
        command:
        - /bin/sh
        - -c
        - |
          echo "验证Pod安全配置..."
          echo "用户ID: $(id -u)"
          echo "组ID: $(id -g)"
          echo "权限检查完成"
          
          # 测试文件系统权限
          if touch /tmp/test-file 2>/dev/null; then
            echo "✅ 临时目录可写"
          else
            echo "❌ 临时目录不可写"
          fi
          
          # 测试网络连接
          if wget -q --spider https://kubernetes.io; then
            echo "✅ 网络连接正常"
          else
            echo "❌ 网络连接失败"
          fi
          
          echo "安全验证完成"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
          seccompProfile:
            type: RuntimeDefault
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
      volumes:
      - name: tmp-volume
        emptyDir: {}
      restartPolicy: Never
  backoffLimit: 3

---
# 安全配置检查ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-checklist
  namespace: restricted-ns
data:
  checklist.yaml: |
    security_checks:
      pod_security:
        - name: "非root用户运行"
          check: "runAsNonRoot: true"
          required: true
        - name: "禁止特权提升"
          check: "allowPrivilegeEscalation: false"
          required: true
        - name: "只读根文件系统"
          check: "readOnlyRootFilesystem: true"
          recommended: true
        - name: "删除所有capabilities"
          check: "capabilities.drop: [ALL]"
          required: true
        - name: "Seccomp配置"
          check: "seccompProfile.type: RuntimeDefault"
          required: true
      resource_limits:
        - name: "CPU限制"
          check: "resources.limits.cpu"
          required: true
        - name: "内存限制"
          check: "resources.limits.memory"
          required: true
        - name: "CPU请求"
          check: "resources.requests.cpu"
          required: true
        - name: "内存请求"
          check: "resources.requests.memory"
          required: true
      network_security:
        - name: "网络策略"
          check: "NetworkPolicy exists"
          required: true
        - name: "服务网格"
          check: "Istio sidecar injection"
          recommended: true
