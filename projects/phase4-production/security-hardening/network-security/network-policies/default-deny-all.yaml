# 默认拒绝所有网络策略
# 实施零信任网络安全模型

---
# 默认拒绝所有入站流量
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all-ingress
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress

---
# 默认拒绝所有出站流量
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all-egress
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Egress

---
# 允许DNS查询的网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-dns-access
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Egress
  egress:
  # 允许DNS查询 (UDP 53)
  - to: []
    ports:
    - protocol: UDP
      port: 53
  # 允许DNS查询 (TCP 53)
  - to: []
    ports:
    - protocol: TCP
      port: 53

---
# 允许访问Kubernetes API的网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-k8s-api-access
  namespace: production
spec:
  podSelector:
    matchLabels:
      needs-k8s-api: "true"
  policyTypes:
  - Egress
  egress:
  # 允许访问Kubernetes API服务器
  - to:
    - namespaceSelector:
        matchLabels:
          name: default
    ports:
    - protocol: TCP
      port: 443

---
# Web应用网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: web-app-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: web-app
      tier: frontend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许来自Ingress控制器的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
  # 允许来自同一命名空间的其他Pod
  - from:
    - podSelector:
        matchLabels:
          environment: production
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # 允许访问后端API
  - to:
    - podSelector:
        matchLabels:
          app: api-server
          tier: backend
    ports:
    - protocol: TCP
      port: 3000
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# API服务器网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-server-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: api-server
      tier: backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许来自前端应用的流量
  - from:
    - podSelector:
        matchLabels:
          app: web-app
          tier: frontend
    ports:
    - protocol: TCP
      port: 3000
  # 允许来自监控系统的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 3000
  egress:
  # 允许访问数据库
  - to:
    - podSelector:
        matchLabels:
          app: database
          tier: data
    ports:
    - protocol: TCP
      port: 5432
  # 允许访问Redis缓存
  - to:
    - podSelector:
        matchLabels:
          app: redis
          tier: cache
    ports:
    - protocol: TCP
      port: 6379
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # 允许HTTPS出站连接（用于外部API调用）
  - to: []
    ports:
    - protocol: TCP
      port: 443

---
# 数据库网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: database-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: database
      tier: data
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 只允许来自API服务器的连接
  - from:
    - podSelector:
        matchLabels:
          app: api-server
          tier: backend
    ports:
    - protocol: TCP
      port: 5432
  # 允许来自备份服务的连接
  - from:
    - podSelector:
        matchLabels:
          app: backup-service
    ports:
    - protocol: TCP
      port: 5432
  egress:
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# 监控系统网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: monitoring-network-policy
  namespace: monitoring
spec:
  podSelector:
    matchLabels:
      app: prometheus
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许来自Grafana的连接
  - from:
    - podSelector:
        matchLabels:
          app: grafana
    ports:
    - protocol: TCP
      port: 9090
  # 允许来自AlertManager的连接
  - from:
    - podSelector:
        matchLabels:
          app: alertmanager
    ports:
    - protocol: TCP
      port: 9090
  egress:
  # 允许抓取所有命名空间的指标
  - to: []
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 3000
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# 跨命名空间通信策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cross-namespace-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      allow-cross-namespace: "true"
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许来自staging命名空间的流量（用于测试）
  - from:
    - namespaceSelector:
        matchLabels:
          name: staging
    - podSelector:
        matchLabels:
          environment: staging
  egress:
  # 允许访问共享服务命名空间
  - to:
    - namespaceSelector:
        matchLabels:
          name: shared-services
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# 开发环境宽松策略（仅用于开发）
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: development-allow-all
  namespace: development
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - {}
  egress:
  - {}
