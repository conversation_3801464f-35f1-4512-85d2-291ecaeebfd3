# 安全的Ingress配置
# 实施TLS加密、访问控制和安全头

---
# 安全的Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: secure-web-app-ingress
  namespace: production
  annotations:
    # 使用NGINX Ingress控制器
    kubernetes.io/ingress.class: "nginx"
    
    # 强制HTTPS重定向
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # 安全头配置
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains";
      more_set_headers "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";
      more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
      more_set_headers "Permissions-Policy: geolocation=(), microphone=(), camera=()";
    
    # 速率限制
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # 客户端体大小限制
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    
    # 超时配置
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "5"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    
    # 白名单IP（可选）
    # nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    
    # 启用CORS（如果需要）
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://trusted-domain.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    
    # 证书管理器注解（如果使用cert-manager）
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  tls:
  - hosts:
    - secure-app.example.com
    - api.example.com
    secretName: secure-app-tls
  rules:
  - host: secure-app.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: web-app-service
            port:
              number: 80
  - host: api.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-service
            port:
              number: 3000

---
# TLS证书Secret（手动管理）
apiVersion: v1
kind: Secret
metadata:
  name: secure-app-tls
  namespace: production
type: kubernetes.io/tls
data:
  # 这里应该包含实际的证书和私钥（base64编码）
  tls.crt: LS0tLS1CRUdJTi... # 证书内容
  tls.key: LS0tLS1CRUdJTi... # 私钥内容

---
# 管理员访问的Ingress（带基本认证）
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: admin-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    
    # 基本认证
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: admin-auth
    nginx.ingress.kubernetes.io/auth-realm: "管理员区域"
    
    # 更严格的IP白名单
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12"
    
    # 更严格的安全头
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
      more_set_headers "Content-Security-Policy: default-src 'none'; script-src 'self'; style-src 'self'; img-src 'self'";
      more_set_headers "Cache-Control: no-store, no-cache, must-revalidate";
      
spec:
  tls:
  - hosts:
    - admin.example.com
    secretName: admin-tls
  rules:
  - host: admin.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: admin-service
            port:
              number: 8080

---
# 管理员认证Secret
apiVersion: v1
kind: Secret
metadata:
  name: admin-auth
  namespace: production
type: Opaque
data:
  # 用户名:密码 (admin:secretpassword) 的htpasswd格式
  auth: YWRtaW46JGFwcjEkSDZ1YjJJYmwkWGtqYjFHWGZCWXNzVnVHMWJCNHI4MA==

---
# 监控Ingress（内部访问）
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: monitoring-ingress
  namespace: monitoring
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    
    # 仅允许内部网络访问
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    
    # OAuth2认证（如果配置了OAuth2 Proxy）
    # nginx.ingress.kubernetes.io/auth-url: "https://oauth2-proxy.example.com/oauth2/auth"
    # nginx.ingress.kubernetes.io/auth-signin: "https://oauth2-proxy.example.com/oauth2/start?rd=$escaped_request_uri"
    
spec:
  tls:
  - hosts:
    - grafana.internal.example.com
    - prometheus.internal.example.com
    secretName: monitoring-tls
  rules:
  - host: grafana.internal.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000
  - host: prometheus.internal.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: prometheus
            port:
              number: 9090

---
# WAF保护的Ingress（使用ModSecurity）
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: waf-protected-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    
    # 启用ModSecurity WAF
    nginx.ingress.kubernetes.io/enable-modsecurity: "true"
    nginx.ingress.kubernetes.io/enable-owasp-core-rules: "true"
    
    # 自定义ModSecurity规则
    nginx.ingress.kubernetes.io/modsecurity-snippet: |
      SecRuleEngine On
      SecRequestBodyAccess On
      SecRule REQUEST_HEADERS:Content-Type "text/xml" \
        "id:'200001',phase:1,t:none,t:lowercase,pass,nolog,ctl:requestBodyProcessor=XML"
      SecRule REQUEST_HEADERS:Content-Type "application/json" \
        "id:'200002',phase:1,t:none,t:lowercase,pass,nolog,ctl:requestBodyProcessor=JSON"
      SecRule ARGS "@detectSQLi" \
        "id:200003,phase:2,block,msg:'SQL Injection Attack Detected',logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',tag:'application-multi',tag:'language-multi',tag:'platform-multi',tag:'attack-sqli'"
    
    # 额外的安全配置
    nginx.ingress.kubernetes.io/configuration-snippet: |
      # 隐藏服务器信息
      more_set_headers "Server: ";
      
      # 防止点击劫持
      more_set_headers "X-Frame-Options: SAMEORIGIN";
      
      # 防止MIME类型嗅探
      more_set_headers "X-Content-Type-Options: nosniff";
      
      # XSS保护
      more_set_headers "X-XSS-Protection: 1; mode=block";
      
      # HSTS
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
      
spec:
  tls:
  - hosts:
    - secure-api.example.com
    secretName: waf-protected-tls
  rules:
  - host: secure-api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: protected-api-service
            port:
              number: 8080
