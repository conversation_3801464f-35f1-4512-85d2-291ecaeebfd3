#!/bin/bash

# Trivy容器安全扫描配置脚本
# 用于设置和配置Trivy进行容器镜像安全扫描

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl未安装，请先安装kubectl"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 安装Trivy
install_trivy() {
    log_info "安装Trivy..."
    
    if command -v trivy &> /dev/null; then
        log_warning "Trivy已安装，跳过安装步骤"
        trivy --version
        return
    fi
    
    # 根据操作系统安装Trivy
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install trivy
        else
            curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
        fi
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    log_success "Trivy安装完成"
    trivy --version
}

# 创建Trivy配置文件
create_trivy_config() {
    log_info "创建Trivy配置文件..."
    
    cat > trivy.yaml << 'EOF'
# Trivy配置文件
format: json
output: trivy-report.json
severity:
  - UNKNOWN
  - LOW
  - MEDIUM
  - HIGH
  - CRITICAL
vulnerability:
  type:
    - os
    - library
ignore-unfixed: false
exit-code: 1
timeout: 10m
cache:
  dir: ~/.cache/trivy
EOF
    
    log_success "Trivy配置文件创建完成"
}

# 创建扫描脚本
create_scan_scripts() {
    log_info "创建扫描脚本..."
    
    # 镜像扫描脚本
    cat > scan-image.sh << 'EOF'
#!/bin/bash

# 镜像安全扫描脚本

set -euo pipefail

if [ $# -eq 0 ]; then
    echo "用法: $0 <镜像名称>"
    echo "示例: $0 nginx:latest"
    exit 1
fi

IMAGE_NAME="$1"
REPORT_DIR="reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
REPORT_FILE="${REPORT_DIR}/scan_${IMAGE_NAME//\//_}_${TIMESTAMP}.json"

# 创建报告目录
mkdir -p "$REPORT_DIR"

echo "开始扫描镜像: $IMAGE_NAME"

# 执行Trivy扫描
trivy image \
    --format json \
    --output "$REPORT_FILE" \
    --severity HIGH,CRITICAL \
    --exit-code 1 \
    "$IMAGE_NAME"

echo "扫描完成，报告保存至: $REPORT_FILE"

# 生成简化报告
trivy image \
    --format table \
    --severity HIGH,CRITICAL \
    "$IMAGE_NAME"
EOF

    chmod +x scan-image.sh
    
    # Kubernetes资源扫描脚本
    cat > scan-k8s.sh << 'EOF'
#!/bin/bash

# Kubernetes资源安全扫描脚本

set -euo pipefail

NAMESPACE="${1:-default}"
REPORT_DIR="reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建报告目录
mkdir -p "$REPORT_DIR"

echo "开始扫描Kubernetes资源 (命名空间: $NAMESPACE)"

# 扫描配置文件
trivy k8s \
    --format json \
    --output "${REPORT_DIR}/k8s_config_scan_${TIMESTAMP}.json" \
    --severity HIGH,CRITICAL \
    cluster

# 扫描运行中的镜像
kubectl get pods -n "$NAMESPACE" -o jsonpath='{.items[*].spec.containers[*].image}' | \
    tr ' ' '\n' | sort -u | while read -r image; do
    if [ -n "$image" ]; then
        echo "扫描镜像: $image"
        trivy image \
            --format json \
            --output "${REPORT_DIR}/runtime_${image//\//_}_${TIMESTAMP}.json" \
            --severity HIGH,CRITICAL \
            "$image" || true
    fi
done

echo "Kubernetes扫描完成，报告保存至: $REPORT_DIR"
EOF

    chmod +x scan-k8s.sh
    
    log_success "扫描脚本创建完成"
}

# 创建CI/CD集成配置
create_cicd_integration() {
    log_info "创建CI/CD集成配置..."
    
    # GitHub Actions配置
    mkdir -p .github/workflows
    cat > .github/workflows/security-scan.yml << 'EOF'
name: Container Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Build Docker image
      run: |
        docker build -t test-image:${{ github.sha }} .
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'test-image:${{ github.sha }}'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run Trivy vulnerability scanner (JSON)
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'test-image:${{ github.sha }}'
        format: 'json'
        output: 'trivy-results.json'
    
    - name: Upload scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: trivy-scan-results
        path: trivy-results.json
EOF
    
    # GitLab CI配置
    cat > .gitlab-ci.yml << 'EOF'
stages:
  - security-scan

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

security-scan:
  stage: security-scan
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - apk add --no-cache curl
    - curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
  script:
    - docker build -t test-image:$CI_COMMIT_SHA .
    - trivy image --exit-code 1 --severity HIGH,CRITICAL test-image:$CI_COMMIT_SHA
  artifacts:
    reports:
      junit: trivy-report.xml
    paths:
      - trivy-report.json
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests
EOF
    
    log_success "CI/CD集成配置创建完成"
}

# 创建策略文件
create_policies() {
    log_info "创建安全策略文件..."
    
    mkdir -p policies
    
    # OPA策略：禁止高危漏洞镜像
    cat > policies/deny-high-vulnerabilities.rego << 'EOF'
package kubernetes.admission

import rego.v1

# 拒绝包含高危漏洞的镜像
deny contains msg if {
    input.request.kind.kind == "Pod"
    container := input.request.object.spec.containers[_]
    has_high_vulnerabilities(container.image)
    msg := sprintf("镜像 %s 包含高危漏洞，部署被拒绝", [container.image])
}

# 检查镜像是否包含高危漏洞（这里需要与实际的漏洞数据库集成）
has_high_vulnerabilities(image) if {
    # 这里应该调用实际的漏洞扫描API
    # 为了演示，我们使用一些已知的有问题的镜像
    vulnerable_images := {
        "nginx:1.14",
        "ubuntu:16.04",
        "node:10"
    }
    vulnerable_images[image]
}
EOF
    
    # Kubernetes网络策略
    cat > policies/network-policy.yaml << 'EOF'
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all-ingress
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 443
EOF
    
    log_success "安全策略文件创建完成"
}

# 主函数
main() {
    log_info "开始设置Trivy容器安全扫描环境..."
    
    check_dependencies
    install_trivy
    create_trivy_config
    create_scan_scripts
    create_cicd_integration
    create_policies
    
    log_success "Trivy安全扫描环境设置完成！"
    
    echo ""
    echo "使用方法："
    echo "1. 扫描单个镜像: ./scan-image.sh nginx:latest"
    echo "2. 扫描Kubernetes集群: ./scan-k8s.sh [namespace]"
    echo "3. 查看配置文件: cat trivy.yaml"
    echo "4. 查看策略文件: ls policies/"
    echo ""
    echo "注意：请根据实际需求调整配置文件和策略"
}

# 执行主函数
main "$@"
