# 🔒 容器镜像安全最佳实践

## 📋 安全构建原则

### 1. 使用最小化基础镜像
```dockerfile
# ❌ 避免使用完整的操作系统镜像
FROM ubuntu:latest

# ✅ 使用精简的Alpine镜像
FROM node:18-alpine

# ✅ 或使用distroless镜像
FROM gcr.io/distroless/nodejs18-debian11
```

### 2. 多阶段构建
```dockerfile
# 构建阶段 - 包含构建工具
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 运行阶段 - 只包含运行时文件
FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
```

### 3. 非root用户运行
```dockerfile
# 创建专用用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# 设置文件权限
COPY --chown=appuser:appgroup . .

# 切换到非特权用户
USER appuser
```

### 4. 最小化攻击面
```dockerfile
# 只安装必要的包
RUN apk add --no-cache \
    curl \
    ca-certificates && \
    rm -rf /var/cache/apk/*

# 删除不必要的文件
RUN rm -rf \
    /tmp/* \
    /var/tmp/* \
    /usr/share/doc/* \
    /usr/share/man/*
```

## 🛡️ 安全配置示例

### Dockerfile安全模板
```dockerfile
# 使用特定版本标签，避免latest
FROM node:18.17.0-alpine3.18

# 安全标签
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL security.scan="trivy"

# 更新系统包
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# 创建应用用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# 设置工作目录
WORKDIR /app

# 复制并安装依赖
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force

# 复制应用代码
COPY --chown=appuser:appgroup . .

# 设置权限
RUN chmod -R 755 /app

# 切换用户
USER appuser

# 使用非特权端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s \
    CMD curl -f http://localhost:3000/health || exit 1

# 使用init进程
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "index.js"]
```

### .dockerignore文件
```
# 版本控制
.git
.gitignore

# 依赖目录
node_modules
npm-debug.log*

# 构建输出
dist
build

# 环境文件
.env
.env.local
.env.*.local

# 日志文件
logs
*.log

# 临时文件
.tmp
.temp

# IDE文件
.vscode
.idea

# 测试文件
test
tests
*.test.js
coverage

# 文档
README.md
docs/

# Docker文件
Dockerfile*
docker-compose*
```

## 🔍 安全扫描集成

### 构建时扫描
```bash
#!/bin/bash
# build-secure.sh

IMAGE_NAME="myapp"
IMAGE_TAG="latest"

# 构建镜像
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .

# 安全扫描
trivy image --exit-code 1 --severity HIGH,CRITICAL ${IMAGE_NAME}:${IMAGE_TAG}

# 如果扫描通过，推送镜像
if [ $? -eq 0 ]; then
    echo "安全扫描通过，推送镜像..."
    docker push ${IMAGE_NAME}:${IMAGE_TAG}
else
    echo "安全扫描失败，停止推送"
    exit 1
fi
```

### CI/CD集成
```yaml
# GitHub Actions示例
name: Secure Build

on: [push, pull_request]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Build image
      run: docker build -t test-image .
    
    - name: Run Trivy scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'test-image'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
```

## 📊 安全检查清单

### 镜像构建安全
- [ ] 使用官方基础镜像
- [ ] 指定具体版本标签
- [ ] 实施多阶段构建
- [ ] 使用最小化镜像
- [ ] 定期更新基础镜像

### 运行时安全
- [ ] 使用非root用户
- [ ] 设置只读文件系统
- [ ] 限制容器权限
- [ ] 使用安全上下文
- [ ] 配置资源限制

### 网络安全
- [ ] 使用非特权端口
- [ ] 最小化暴露端口
- [ ] 实施网络策略
- [ ] 配置TLS加密
- [ ] 限制出站连接

### 数据安全
- [ ] 加密敏感数据
- [ ] 使用密钥管理
- [ ] 避免硬编码密钥
- [ ] 实施数据备份
- [ ] 配置访问控制

## 🚨 常见安全问题

### 1. 使用root用户运行
```dockerfile
# ❌ 危险做法
USER root
RUN chmod 777 /app

# ✅ 安全做法
RUN adduser -D -s /bin/sh appuser
USER appuser
```

### 2. 暴露敏感信息
```dockerfile
# ❌ 避免在镜像中包含密钥
ENV API_KEY=secret123
COPY .env /app/

# ✅ 使用运行时注入
# 通过环境变量或密钥管理系统注入
```

### 3. 使用latest标签
```dockerfile
# ❌ 不确定的版本
FROM node:latest

# ✅ 指定具体版本
FROM node:18.17.0-alpine3.18
```

### 4. 安装不必要的包
```dockerfile
# ❌ 安装过多包
RUN apt-get update && apt-get install -y \
    curl wget vim git build-essential

# ✅ 只安装必要的包
RUN apk add --no-cache curl ca-certificates
```

## 🔧 安全工具推荐

### 静态分析工具
- **Trivy**: 容器漏洞扫描
- **Grype**: 容器镜像安全扫描
- **Clair**: 静态漏洞分析
- **Snyk**: 依赖漏洞检测

### 运行时安全
- **Falco**: 运行时威胁检测
- **Sysdig**: 容器运行时监控
- **Aqua**: 容器安全平台
- **Twistlock**: 云原生安全

### 镜像签名
- **Cosign**: 容器镜像签名
- **Notary**: Docker镜像签名
- **TUF**: 更新框架
- **in-toto**: 供应链安全

---

**记住：安全是一个持续的过程，不是一次性的任务！** 🛡️
