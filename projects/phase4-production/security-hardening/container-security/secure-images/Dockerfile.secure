# 安全的多阶段Docker镜像构建示例
# 展示容器安全最佳实践

# 第一阶段：构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 只复制package文件，利用Docker缓存
COPY package*.json ./

# 安装依赖（包括开发依赖）
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 第二阶段：运行阶段
FROM node:18-alpine AS runtime

# 安全标签
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="安全加固的Node.js应用镜像"

# 更新系统包并安装安全更新
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
        dumb-init \
        curl && \
    rm -rf /var/cache/apk/*

# 创建非特权用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# 设置工作目录
WORKDIR /app

# 复制构建产物（只复制必要文件）
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

# 设置正确的文件权限
RUN chmod -R 755 /app && \
    chown -R nextjs:nodejs /app

# 切换到非特权用户
USER nextjs

# 暴露端口（使用非特权端口）
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 使用dumb-init作为PID 1，处理信号
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "dist/index.js"]
