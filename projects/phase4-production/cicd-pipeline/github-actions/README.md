# GitHub Actions CI/CD 配置

> 本目录包含 GitHub Actions 相关的配置文件、脚本和文档

## 📁 目录结构

```
github-actions/
├── README.md              # 本文档
├── workflows/             # 工作流模板
│   ├── basic-ci.yml      # 基础CI模板
│   ├── advanced-cd.yml   # 高级CD模板
│   └── security-scan.yml # 安全扫描模板
├── scripts/              # 自定义脚本
│   ├── build.sh         # 构建脚本
│   ├── deploy.sh        # 部署脚本
│   └── test.sh          # 测试脚本
└── docs/                 # 文档
    ├── best-practices.md # 最佳实践
    ├── troubleshooting.md # 故障排除
    └── examples.md       # 示例配置
```

## 🚀 当前活跃的工作流

实际的 GitHub Actions 工作流文件位于项目根目录：

- **主要工作流**: `/.github/workflows/sample-app-ci-cd.yml`
  - 自动化测试、构建和部署
  - 支持多环境部署
  - 集成安全扫描
  - 支持手动触发

## 📋 工作流功能

### 1. 持续集成 (CI)
- ✅ 代码检出和环境设置
- ✅ 依赖安装和缓存
- ✅ 单元测试执行
- ✅ 代码覆盖率报告
- ✅ 安全漏洞扫描

### 2. 持续部署 (CD)
- ✅ Docker 镜像构建
- ✅ 镜像推送到 GHCR
- ✅ Kubernetes 部署
- ✅ 健康检查验证

### 3. 安全集成
- ✅ 依赖漏洞扫描 (npm audit)
- ✅ 容器镜像扫描 (Trivy)
- ✅ 代码质量检查

## 🔧 使用指南

### 查看工作流状态
```bash
# 在 GitHub 仓库页面查看
https://github.com/your-username/your-repo/actions

# 或使用 GitHub CLI
gh workflow list
gh run list
```

### 手动触发工作流
```bash
# 使用 GitHub CLI
gh workflow run "Sample App CI/CD Pipeline"

# 或在 GitHub 网页界面点击 "Run workflow"
```

### 本地测试工作流
```bash
# 使用 act 工具本地运行 GitHub Actions
act -j test  # 运行测试作业
act -j build # 运行构建作业
```

## 📊 监控和调试

### 查看工作流日志
1. 访问 GitHub Actions 页面
2. 点击具体的工作流运行
3. 查看各个步骤的详细日志

### 常见问题排查
- **构建失败**: 检查依赖版本和环境配置
- **测试失败**: 查看测试日志和覆盖率报告
- **部署失败**: 验证 Kubernetes 配置和权限

## 🔐 密钥管理

### 必需的 Secrets
- `KUBECONFIG`: Kubernetes 集群配置
- `GITHUB_TOKEN`: 自动提供，用于推送镜像

### 环境变量
- `REGISTRY`: 容器镜像仓库地址
- `IMAGE_NAME`: 镜像名称
- `NODE_VERSION`: Node.js 版本

## 📈 性能优化

### 缓存策略
- ✅ Node.js 依赖缓存
- ✅ Docker 层缓存
- ✅ 构建产物缓存

### 并行执行
- ✅ 测试和构建并行运行
- ✅ 多环境并行部署
- ✅ 安全扫描并行执行

## 🎯 最佳实践

1. **工作流设计**
   - 保持工作流简洁明了
   - 使用可重用的 Actions
   - 合理设置触发条件

2. **安全考虑**
   - 最小权限原则
   - 密钥轮换
   - 审计日志记录

3. **性能优化**
   - 合理使用缓存
   - 避免不必要的步骤
   - 优化构建时间

## 🔗 相关链接

- [GitHub Actions 官方文档](https://docs.github.com/en/actions)
- [工作流语法参考](https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions)
- [Marketplace Actions](https://github.com/marketplace?type=actions)
- [最佳实践指南](https://docs.github.com/en/actions/security-guides/security-hardening-for-github-actions)

---

**注意**: 本目录用于存放 GitHub Actions 相关的模板、脚本和文档。实际的工作流文件位于 `/.github/workflows/` 目录中。
