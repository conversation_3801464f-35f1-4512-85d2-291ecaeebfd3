#!/bin/bash

# =============================================================================
# GitHub Actions 部署脚本
# 用于自动化部署到 Kubernetes 集群
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 参数检查
if [ $# -lt 3 ]; then
    echo -e "${RED}错误: 参数不足${NC}"
    echo "用法: $0 <环境> <镜像标签> <应用路径>"
    echo "示例: $0 staging v1.0.0 ./k8s"
    exit 1
fi

ENVIRONMENT="$1"
IMAGE_TAG="$2"
APP_PATH="$3"
NAMESPACE="${ENVIRONMENT}"

echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}                    GitHub Actions 部署脚本${NC}"
echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}环境: $ENVIRONMENT${NC}"
echo -e "${BLUE}镜像标签: $IMAGE_TAG${NC}"
echo -e "${BLUE}应用路径: $APP_PATH${NC}"
echo -e "${BLUE}命名空间: $NAMESPACE${NC}"
echo

# 检查必要工具
check_tool() {
    local tool="$1"
    if ! command -v "$tool" &> /dev/null; then
        echo -e "${RED}错误: $tool 未安装${NC}"
        exit 1
    fi
}

echo -e "${YELLOW}🔧 检查必要工具...${NC}"
check_tool kubectl
check_tool envsubst
echo -e "${GREEN}✅ 工具检查完成${NC}"

# 检查 Kubernetes 连接
echo -e "${YELLOW}🔗 检查 Kubernetes 连接...${NC}"
if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}错误: 无法连接到 Kubernetes 集群${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Kubernetes 连接正常${NC}"

# 创建命名空间（如果不存在）
echo -e "${YELLOW}📦 准备命名空间...${NC}"
kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
echo -e "${GREEN}✅ 命名空间 $NAMESPACE 已准备${NC}"

# 设置环境变量用于模板替换
export IMAGE_TAG
export NAMESPACE
export ENVIRONMENT

# 部署应用
echo -e "${YELLOW}🚀 开始部署应用...${NC}"

# 处理 Kubernetes 清单文件
for file in "$APP_PATH"/*.yaml "$APP_PATH"/*.yml; do
    if [ -f "$file" ]; then
        echo -e "${BLUE}📄 处理文件: $(basename "$file")${NC}"
        
        # 使用 envsubst 替换环境变量
        envsubst < "$file" | kubectl apply -f - -n "$NAMESPACE"
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ $(basename "$file") 部署成功${NC}"
        else
            echo -e "${RED}❌ $(basename "$file") 部署失败${NC}"
            exit 1
        fi
    fi
done

# 等待部署完成
echo -e "${YELLOW}⏳ 等待部署完成...${NC}"
kubectl rollout status deployment -n "$NAMESPACE" --timeout=300s

# 验证部署
echo -e "${YELLOW}🔍 验证部署状态...${NC}"
kubectl get pods -n "$NAMESPACE" -l app=sample-app

# 检查服务状态
echo -e "${YELLOW}🌐 检查服务状态...${NC}"
kubectl get services -n "$NAMESPACE"

# 运行健康检查
echo -e "${YELLOW}🏥 运行健康检查...${NC}"
SERVICE_NAME="sample-app"
if kubectl get service "$SERVICE_NAME" -n "$NAMESPACE" &> /dev/null; then
    # 端口转发进行健康检查
    kubectl port-forward service/"$SERVICE_NAME" 8080:80 -n "$NAMESPACE" &
    PF_PID=$!
    sleep 5
    
    if curl -f http://localhost:8080/health &> /dev/null; then
        echo -e "${GREEN}✅ 健康检查通过${NC}"
    else
        echo -e "${RED}❌ 健康检查失败${NC}"
        kill $PF_PID 2>/dev/null || true
        exit 1
    fi
    
    kill $PF_PID 2>/dev/null || true
else
    echo -e "${YELLOW}⚠️  服务 $SERVICE_NAME 不存在，跳过健康检查${NC}"
fi

echo
echo -e "${GREEN}🎉 部署完成！${NC}"
echo -e "${BLUE}环境: $ENVIRONMENT${NC}"
echo -e "${BLUE}镜像: $IMAGE_TAG${NC}"
echo -e "${BLUE}命名空间: $NAMESPACE${NC}"

# 显示有用的命令
echo
echo -e "${BLUE}💡 有用的命令:${NC}"
echo "  查看 Pod 状态: kubectl get pods -n $NAMESPACE"
echo "  查看服务状态: kubectl get services -n $NAMESPACE"
echo "  查看日志: kubectl logs -l app=sample-app -n $NAMESPACE"
echo "  端口转发: kubectl port-forward service/$SERVICE_NAME 8080:80 -n $NAMESPACE"
