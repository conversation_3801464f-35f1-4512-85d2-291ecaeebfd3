apiVersion: v1
kind: Service
metadata:
  name: sample-app
  labels:
    app: sample-app
    version: v1
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: sample-app
---
apiVersion: v1
kind: Service
metadata:
  name: sample-app-internal
  labels:
    app: sample-app
    version: v1
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: sample-app
