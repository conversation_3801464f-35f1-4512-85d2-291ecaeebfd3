apiVersion: v1
kind: ConfigMap
metadata:
  name: sample-app-config
  labels:
    app: sample-app
data:
  # 应用配置
  NODE_ENV: "production"
  PORT: "3000"
  APP_VERSION: "1.0.0"
  
  # 日志配置
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  
  # 健康检查配置
  HEALTH_CHECK_INTERVAL: "30"
  READINESS_CHECK_TIMEOUT: "5"
  
  # 应用特定配置
  MAX_USERS: "1000"
  RATE_LIMIT: "100"
  
  # 监控配置
  METRICS_ENABLED: "true"
  METRICS_PORT: "9090"
