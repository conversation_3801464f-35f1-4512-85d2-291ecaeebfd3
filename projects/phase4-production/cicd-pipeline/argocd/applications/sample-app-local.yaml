apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: sample-app-local
  namespace: argocd
  labels:
    app: sample-app
    environment: local
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/gitwyy/cloud-native-learning
    targetRevision: HEAD
    path: projects/phase4-production/cicd-pipeline/sample-app/k8s
    directory:
      include: '{deployment-local.yaml,service.yaml,configmap.yaml}'
  destination:
    server: https://kubernetes.default.svc
    namespace: default
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
