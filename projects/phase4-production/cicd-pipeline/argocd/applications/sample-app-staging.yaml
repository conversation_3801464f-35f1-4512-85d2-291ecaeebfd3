apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: sample-app-staging
  namespace: argocd
  labels:
    app: sample-app
    environment: staging
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: sample-app-project
  source:
    repoURL: https://github.com/gitwyy/cloud-native-learning
    targetRevision: HEAD
    path: projects/phase4-production/cicd-pipeline/sample-app/k8s
  destination:
    server: https://kubernetes.default.svc
    namespace: staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
    - group: apps
      kind: Deployment
      jsonPointers:
        - /spec/replicas
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: sample-app-production
  namespace: argocd
  labels:
    app: sample-app
    environment: production
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: sample-app-project
  source:
    repoURL: https://github.com/gitwyy/cloud-native-learning
    targetRevision: HEAD
    path: projects/phase4-production/cicd-pipeline/sample-app/k8s
  destination:
    server: https://kubernetes.default.svc
    namespace: production
  syncPolicy:
    # 生产环境不自动同步，需要手动触发
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
