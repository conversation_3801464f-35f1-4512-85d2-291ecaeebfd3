# 电商微服务完整部署清单
# 此文件包含部署电商应用到Kubernetes所需的所有资源定义

# ===================================
# 命名空间
# ===================================
apiVersion: v1
kind: Namespace
metadata:
  name: ecommerce
  labels:
    name: ecommerce
    purpose: microservices-demo
---
# ===================================
# 配置和密钥
# ===================================
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: ecommerce
type: Opaque
data:
  username: cG9zdGdyZXM=  # postgres
  password: ZWNvbW1lcmNlMTIz  # ecommerce123
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: ecommerce
data:
  database_url: "********************************************************"
  redis_url: "redis://:redis123@redis-service:6379"
  rabbitmq_url: "amqp://admin:rabbitmq123@rabbitmq-service:5672/"
  flask_env: "production"
  log_level: "INFO"
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
  namespace: ecommerce
type: Opaque
data:
  jwt_secret: "dXNlci1zZXJ2aWNlLXNlY3JldC1rZXk="  # user-service-secret-key
  redis_password: "cmVkaXMxMjM="  # redis123
  rabbitmq_password: "cmFiYml0bXExMjM="  # rabbitmq123
---
# ===================================
# 持久化存储
# ===================================
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: ecommerce
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
  storageClassName: standard
---
# ===================================
# PostgreSQL 数据库
# ===================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: ecommerce
  labels:
    app: postgres
    tier: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        tier: database
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        - name: POSTGRES_DB
          value: ecommerce
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: ecommerce
  labels:
    app: postgres
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  type: ClusterIP
---
# ===================================
# Redis 缓存
# ===================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ecommerce
  labels:
    app: redis
    tier: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        tier: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command: ["redis-server"]
        args: 
        - --appendonly
        - "yes"
        - --requirepass
        - "redis123"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: ecommerce
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  type: ClusterIP
---
# ===================================
# RabbitMQ 消息队列
# ===================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: ecommerce
  labels:
    app: rabbitmq
    tier: queue
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
        tier: queue
    spec:
      containers:
      - name: rabbitmq
        image: rabbitmq:3.12-management-alpine
        ports:
        - containerPort: 5672
          name: amqp
        - containerPort: 15672
          name: management
        env:
        - name: RABBITMQ_DEFAULT_USER
          value: "admin"
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: rabbitmq_password
        - name: RABBITMQ_DEFAULT_VHOST
          value: "/"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-service
  namespace: ecommerce
  labels:
    app: rabbitmq
spec:
  selector:
    app: rabbitmq
  ports:
  - port: 5672
    targetPort: 5672
    name: amqp
  - port: 15672
    targetPort: 15672
    name: management
  type: ClusterIP
---
# ===================================
# 用户服务
# ===================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: ecommerce
  labels:
    app: user-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        tier: backend
        version: v1.0
    spec:
      containers:
      - name: user-service
        image: user-service:1.0
        imagePullPolicy: Never  # 使用本地镜像
        ports:
        - containerPort: 5001
          name: http
        env:
        - name: DATABASE_URL
          value: "********************************************************/ecommerce_users"
        - name: REDIS_URL
          value: "redis://:redis123@redis-service:6379/0"
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: rabbitmq_url
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: flask_env
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: jwt_secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          httpGet:
            path: /health
            port: 5001
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 5001
          initialDelaySeconds: 30
          periodSeconds: 15
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: ecommerce
  labels:
    app: user-service
spec:
  selector:
    app: user-service
  ports:
  - name: http
    port: 80
    targetPort: 5001
  type: ClusterIP
---
# ===================================
# 商品服务
# ===================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: product-service
  namespace: ecommerce
  labels:
    app: product-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: product-service
  template:
    metadata:
      labels:
        app: product-service
        tier: backend
        version: v1.0
    spec:
      containers:
      - name: product-service
        image: product-service:1.0
        imagePullPolicy: Never
        ports:
        - containerPort: 5002
          name: http
        env:
        - name: DATABASE_URL
          value: "********************************************************/ecommerce_products"
        - name: REDIS_URL
          value: "redis://:redis123@redis-service:6379/1"
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: rabbitmq_url
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: flask_env
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          httpGet:
            path: /health
            port: 5002
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 5002
          initialDelaySeconds: 30
          periodSeconds: 15
---
apiVersion: v1
kind: Service
metadata:
  name: product-service
  namespace: ecommerce
  labels:
    app: product-service
spec:
  selector:
    app: product-service
  ports:
  - name: http
    port: 80
    targetPort: 5002
  type: ClusterIP
---
# ===================================
# 订单服务
# ===================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: order-service
  namespace: ecommerce
  labels:
    app: order-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: order-service
  template:
    metadata:
      labels:
        app: order-service
        tier: backend
        version: v1.0
    spec:
      containers:
      - name: order-service
        image: order-service:1.0
        imagePullPolicy: Never
        ports:
        - containerPort: 5003
          name: http
        env:
        - name: DATABASE_URL
          value: "********************************************************/ecommerce_orders"
        - name: REDIS_URL
          value: "redis://:redis123@redis-service:6379/2"
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: rabbitmq_url
        - name: USER_SERVICE_URL
          value: "http://user-service:80"
        - name: PRODUCT_SERVICE_URL
          value: "http://product-service:80"
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: flask_env
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          httpGet:
            path: /health
            port: 5003
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 5003
          initialDelaySeconds: 30
          periodSeconds: 15
---
apiVersion: v1
kind: Service
metadata:
  name: order-service
  namespace: ecommerce
  labels:
    app: order-service
spec:
  selector:
    app: order-service
  ports:
  - name: http
    port: 80
    targetPort: 5003
  type: ClusterIP
---
# ===================================
# 通知服务
# ===================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  namespace: ecommerce
  labels:
    app: notification-service
    tier: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
        tier: backend
        version: v1.0
    spec:
      containers:
      - name: notification-service
        image: notification-service:1.0
        imagePullPolicy: Never
        ports:
        - containerPort: 5004
          name: http
        env:
        - name: DATABASE_URL
          value: "********************************************************/ecommerce_notifications"
        - name: REDIS_URL
          value: "redis://:redis123@redis-service:6379/3"
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: rabbitmq_url
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: flask_env
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          httpGet:
            path: /health
            port: 5004
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 5004
          initialDelaySeconds: 30
          periodSeconds: 15
---
apiVersion: v1
kind: Service
metadata:
  name: notification-service
  namespace: ecommerce
  labels:
    app: notification-service
spec:
  selector:
    app: notification-service
  ports:
  - name: http
    port: 80
    targetPort: 5004
  type: ClusterIP
---
# ===================================
# API 网关 (Nginx)
# ===================================
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: ecommerce
data:
  default.conf: |
    upstream user_service {
        server user-service:80;
    }
    upstream product_service {
        server product-service:80;
    }
    upstream order_service {
        server order-service:80;
    }
    upstream notification_service {
        server notification-service:80;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /health {
            access_log off;
            return 200 "API Gateway Healthy\n";
            add_header Content-Type text/plain;
        }
        
        # 用户服务路由
        location ~ ^/api/v1/(register|login|profile|logout|users) {
            proxy_pass http://user_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # 商品服务路由
        location ~ ^/api/v1/(products|categories) {
            proxy_pass http://product_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # 订单服务路由
        location ~ ^/api/v1/orders {
            proxy_pass http://order_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # 通知服务路由
        location ~ ^/api/v1/(notifications|templates) {
            proxy_pass http://notification_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # 健康检查路由
        location /health/user { proxy_pass http://user_service/health; }
        location /health/product { proxy_pass http://product_service/health; }
        location /health/order { proxy_pass http://order_service/health; }
        location /health/notification { proxy_pass http://notification_service/health; }
        
        location / {
            return 200 '
<!DOCTYPE html>
<html>
<head><title>电商微服务 - Kubernetes部署</title></head>
<body style="font-family: Arial; margin: 40px;">
    <h1>🛒 电商微服务系统</h1>
    <p>Kubernetes集群部署版本</p>
    <h3>服务健康检查:</h3>
    <ul>
        <li><a href="/health/user">用户服务</a></li>
        <li><a href="/health/product">商品服务</a></li>
        <li><a href="/health/order">订单服务</a></li>
        <li><a href="/health/notification">通知服务</a></li>
    </ul>
    <h3>API 端点:</h3>
    <ul>
        <li>POST /api/v1/register - 用户注册</li>
        <li>POST /api/v1/login - 用户登录</li>
        <li>GET /api/v1/products - 商品列表</li>
        <li>POST /api/v1/orders - 创建订单</li>
    </ul>
</body>
</html>
            ';
            add_header Content-Type text/html;
        }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: ecommerce
  labels:
    app: api-gateway
    tier: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        tier: frontend
        version: v1.0
    spec:
      containers:
      - name: nginx
        image: nginx:1.25-alpine
        ports:
        - containerPort: 80
          name: http
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: ecommerce
  labels:
    app: api-gateway
spec:
  selector:
    app: api-gateway
  ports:
  - name: http
    port: 80
    targetPort: 80
    nodePort: 30080
  type: NodePort