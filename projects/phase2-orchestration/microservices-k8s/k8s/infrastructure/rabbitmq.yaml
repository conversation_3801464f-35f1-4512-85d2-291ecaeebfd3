# RabbitMQ消息队列服务配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: ecommerce-k8s
  labels:
    app: rabbitmq
    tier: infrastructure
    component: message-queue
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  strategy:
    type: Recreate  # RabbitMQ使用Recreate策略确保数据一致性
  template:
    metadata:
      labels:
        app: rabbitmq
        tier: infrastructure
        component: message-queue
        version: v3.12
    spec:
      containers:
      - name: rabbitmq
        image: rabbitmq:3.12-alpine
        ports:
        - containerPort: 5672
          name: amqp
          protocol: TCP

        env:
        - name: RABBITMQ_DEFAULT_USER
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: rabbitmq_user
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: rabbitmq_password
        - name: RABBITMQ_DEFAULT_VHOST
          value: "ecommerce"
        - name: RABBITMQ_ERLANG_COOKIE
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: rabbitmq_erlang_cookie
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        readinessProbe:
          exec:
            command:
            - rabbitmq-diagnostics
            - ping
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          exec:
            command:
            - rabbitmq-diagnostics
            - status
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          exec:
            command:
            - rabbitmq-diagnostics
            - ping
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 30
        volumeMounts:
        - name: rabbitmq-data
          mountPath: /var/lib/rabbitmq
        - name: rabbitmq-config
          mountPath: /etc/rabbitmq/conf.d
        - name: rabbitmq-logs
          mountPath: /var/log/rabbitmq
      volumes:
      - name: rabbitmq-data
        persistentVolumeClaim:
          claimName: rabbitmq-data-pvc
      - name: rabbitmq-config
        configMap:
          name: rabbitmq-config
      - name: rabbitmq-logs
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 60
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq
  namespace: ecommerce-k8s
  labels:
    app: rabbitmq
    tier: infrastructure
    component: message-queue
spec:
  selector:
    app: rabbitmq
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
    protocol: TCP
  - name: management
    port: 15672
    targetPort: 15672
    protocol: TCP
  type: ClusterIP
  sessionAffinity: None
---
# RabbitMQ管理界面服务（可选，用于开发调试）
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-management
  namespace: ecommerce-k8s
  labels:
    app: rabbitmq
    tier: infrastructure
    component: management
spec:
  selector:
    app: rabbitmq
  ports:
  - name: management
    port: 15672
    targetPort: 15672
    protocol: TCP
  type: NodePort
  sessionAffinity: None
---
# RabbitMQ配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
  namespace: ecommerce-k8s
  labels:
    app: rabbitmq
    tier: infrastructure
data:
  10-defaults.conf: |
    # RabbitMQ基础配置
    default_vhost = ecommerce
    default_user_tags.administrator = true

    # 内存和磁盘限制
    vm_memory_high_watermark.relative = 0.6
    disk_free_limit.relative = 2.0

    # 日志配置
    log.console = true
    log.console.level = info
    log.file = false



    # 性能优化
    channel_max = 2047
    connection_max = 1000
    heartbeat = 60
    

  30-clustering.conf: |
    # 单节点模式配置
    cluster_formation.peer_discovery_backend = classic_config
---
# RabbitMQ网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: rabbitmq-netpol
  namespace: ecommerce-k8s
  labels:
    app: rabbitmq
    tier: infrastructure
spec:
  podSelector:
    matchLabels:
      app: rabbitmq
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          tier: backend
    ports:
    - protocol: TCP
      port: 5672
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 15672
  egress:
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # 允许集群通信
  - to:
    - podSelector:
        matchLabels:
          app: rabbitmq
    ports:
    - protocol: TCP
      port: 25672
    - protocol: TCP
      port: 4369
