# API网关部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: ecommerce-k8s
  labels:
    app: api-gateway
    tier: frontend
    component: gateway
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-gateway
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: api-gateway
        tier: frontend
        component: gateway
        version: v1.0
    spec:
      containers:
      - name: nginx
        image: nginx:1.25-alpine
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: nginx-default-config
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
        - name: nginx-proxy-params
          mountPath: /etc/nginx/proxy_params
          subPath: proxy_params
        - name: static-files
          mountPath: /var/www/static
        - name: uploads
          mountPath: /var/uploads
        - name: nginx-logs
          mountPath: /var/log/nginx
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        readinessProbe:
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 3
          timeoutSeconds: 2
          successThreshold: 1
          failureThreshold: 10
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-simple-config
          items:
          - key: nginx.conf
            path: nginx.conf
      - name: nginx-default-config
        configMap:
          name: nginx-simple-config
          items:
          - key: default.conf
            path: default.conf
      - name: nginx-proxy-params
        configMap:
          name: nginx-simple-config
          items:
          - key: proxy_params
            path: proxy_params
      - name: static-files
        persistentVolumeClaim:
          claimName: static-files-pvc
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
      - name: nginx-logs
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: ecommerce-k8s
  labels:
    app: api-gateway
    tier: frontend
spec:
  selector:
    app: api-gateway
  ports:
  - name: http
    port: 80
    targetPort: 80
    nodePort: 30080
    protocol: TCP
  type: NodePort
  sessionAffinity: None
---
# API网关HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: ecommerce-k8s
  labels:
    app: api-gateway
    tier: frontend
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30
      - type: Pods
        value: 2
        periodSeconds: 30
      selectPolicy: Max
---
# API网关PDB配置
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-gateway-pdb
  namespace: ecommerce-k8s
  labels:
    app: api-gateway
    tier: frontend
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: api-gateway
---
# 简化的Nginx配置（避免YAML解析问题）
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-simple-config
  namespace: ecommerce-k8s
  labels:
    app: api-gateway
    tier: frontend
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 100M;
        
        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        upstream user-service {
            server user-service:80;
        }
        
        upstream product-service {
            server product-service:80;
        }
        
        upstream order-service {
            server order-service:80;
        }
        
        upstream notification-service {
            server notification-service:80;
        }
        
        include /etc/nginx/conf.d/*.conf;
    }
  
  default.conf: |
    server {
        listen 80;
        server_name localhost;
        
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log;
        
        location /health {
            access_log off;
            return 200 "API Gateway is healthy\n";
            add_header Content-Type text/plain;
        }
        
        location /api/v1/register {
            proxy_pass http://user-service;
            include /etc/nginx/proxy_params;
        }
        
        location /api/v1/login {
            proxy_pass http://user-service;
            include /etc/nginx/proxy_params;
        }
        
        location ~ ^/api/v1/(profile|logout|users) {
            proxy_pass http://user-service;
            include /etc/nginx/proxy_params;
        }
        
        location ~ ^/api/v1/(products|categories) {
            proxy_pass http://product-service;
            include /etc/nginx/proxy_params;
        }
        
        location ~ ^/api/v1/orders {
            proxy_pass http://order-service;
            include /etc/nginx/proxy_params;
        }
        
        location ~ ^/api/v1/(notifications|templates) {
            proxy_pass http://notification-service;
            include /etc/nginx/proxy_params;
        }
        
        location /health/user {
            proxy_pass http://user-service/health;
            include /etc/nginx/proxy_params;
        }
        
        location /health/product {
            proxy_pass http://product-service/health;
            include /etc/nginx/proxy_params;
        }
        
        location /health/order {
            proxy_pass http://order-service/health;
            include /etc/nginx/proxy_params;
        }
        
        location /health/notification {
            proxy_pass http://notification-service/health;
            include /etc/nginx/proxy_params;
        }
        
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        location /uploads/ {
            alias /var/uploads/;
            expires 30d;
            add_header Cache-Control "public";
        }
        
        location / {
            return 200 '<h1>电商微服务 API Gateway</h1><p>Kubernetes部署版本</p><p><a href="/health">健康检查</a></p>';
            add_header Content-Type text/html;
        }
        
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            return 404 'Not Found';
        }
        
        location = /50x.html {
            return 500 'Server Error';
        }
    }
  
  proxy_params: |
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    proxy_connect_timeout 30s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    proxy_buffering on;
    proxy_buffer_size 8k;
    proxy_buffers 16 8k;
    
    proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    proxy_next_upstream_tries 3;
    proxy_next_upstream_timeout 30s;
    
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_redirect off;