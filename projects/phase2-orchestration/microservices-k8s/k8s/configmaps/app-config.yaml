# 微服务应用配置映射
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: ecommerce-k8s
  labels:
    tier: backend
    purpose: application-config
data:
  # 应用基础配置
  flask_env: "production"
  log_level: "INFO"
  debug_mode: "false"
  timezone: "Asia/Shanghai"
  
  # 数据库配置
  database_host: "postgres-service"
  database_port: "5432"
  database_name: "ecommerce"
  
  # 服务专用数据库
  user_database_url: "********************************************************/ecommerce_users"
  product_database_url: "********************************************************/ecommerce_products"
  order_database_url: "********************************************************/ecommerce_orders"
  notification_database_url: "********************************************************/ecommerce_notifications"
  
  # Redis配置
  redis_host: "redis-service"
  redis_port: "6379"
  redis_password: "redis123"
  
  # Redis数据库分离
  redis_user_url: "redis://:redis123@redis-service:6379/0"
  redis_product_url: "redis://:redis123@redis-service:6379/1"
  redis_order_url: "redis://:redis123@redis-service:6379/2"
  redis_notification_url: "redis://:redis123@redis-service:6379/3"
  redis_session_url: "redis://:redis123@redis-service:6379/4"
  
  # RabbitMQ配置
  rabbitmq_host: "rabbitmq-service"
  rabbitmq_port: "5672"
  rabbitmq_management_port: "15672"
  rabbitmq_username: "admin"
  rabbitmq_password: "rabbitmq123"
  rabbitmq_vhost: "/"
  rabbitmq_url: "amqp://admin:rabbitmq123@rabbitmq-service:5672/"
  
  # 微服务间通信配置
  user_service_url: "http://user-service:80"
  product_service_url: "http://product-service:80"
  order_service_url: "http://order-service:80"
  notification_service_url: "http://notification-service:80"
  
  # API网关配置
  api_gateway_url: "http://api-gateway:80"
  
  # 外部服务配置
  external_payment_api_url: "https://api.payment-provider.com"
  external_logistics_api_url: "https://api.logistics-provider.com"
  
  # 邮件服务配置
  smtp_host: "smtp.gmail.com"
  smtp_port: "587"
  smtp_use_tls: "true"
  smtp_from_name: "电商平台"
  smtp_from_address: "<EMAIL>"
  
  # 短信服务配置
  sms_provider: "aliyun"
  sms_region: "cn-hangzhou"
  
  # 文件存储配置
  upload_path: "/var/uploads"
  max_upload_size: "10485760"  # 10MB
  allowed_extensions: "jpg,jpeg,png,gif,pdf,doc,docx"
  
  # 安全配置
  jwt_algorithm: "HS256"
  jwt_access_token_expires: "86400"  # 24小时
  jwt_refresh_token_expires: "604800"  # 7天
  
  # 限流配置
  rate_limit_per_minute: "60"
  rate_limit_per_hour: "1000"
  rate_limit_per_day: "10000"
  
  # 监控配置
  metrics_enabled: "true"
  health_check_interval: "30"
  
  # 业务配置
  default_currency: "CNY"
  tax_rate: "0.08"
  free_shipping_threshold: "100.00"
  order_timeout_minutes: "30"
  
  # 缓存配置
  cache_ttl_default: "3600"  # 1小时
  cache_ttl_user_session: "1800"  # 30分钟
  cache_ttl_product_list: "300"  # 5分钟
  cache_ttl_static_content: "86400"  # 24小时
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: ecommerce-k8s
  labels:
    app: api-gateway
    tier: frontend
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # 日志格式
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for" '
                        'rt=$request_time uct="$upstream_connect_time" '
                        'uht="$upstream_header_time" urt="$upstream_response_time"';
        
        access_log /var/log/nginx/access.log main;
        
        # 基本设置
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 100M;
        
        # Gzip压缩
        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        
        # 上游服务器定义
        upstream user-service {
            least_conn;
            server user-service:80 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        upstream product-service {
            least_conn;
            server product-service:80 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        upstream order-service {
            least_conn;
            server order-service:80 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        upstream notification-service {
            least_conn;
            server notification-service:80 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        # 限流配置
        limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;
        
        # 包含站点配置
        include /etc/nginx/conf.d/*.conf;
    }
  
  default.conf: |
    server {
        listen 80;
        server_name localhost;
        
        # 访问日志
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log;
        
        # 健康检查端点
        location /health {
            access_log off;
            return 200 "API Gateway is healthy\n";
            add_header Content-Type text/plain;
        }
        
        # 限流配置
        location /api/v1/login {
            limit_req zone=auth_limit burst=10 nodelay;
            proxy_pass http://user-service;
            include /etc/nginx/proxy_params;
        }
        
        location /api/v1/register {
            limit_req zone=auth_limit burst=5 nodelay;
            proxy_pass http://user-service;
            include /etc/nginx/proxy_params;
        }
        
        # 用户服务路由
        location ~ ^/api/v1/(profile|logout|users) {
            limit_req zone=api_limit burst=20 nodelay;
            proxy_pass http://user-service;
            include /etc/nginx/proxy_params;
        }
        
        # 商品服务路由
        location ~ ^/api/v1/(products|categories) {
            limit_req zone=api_limit burst=50 nodelay;
            proxy_pass http://product-service;
            include /etc/nginx/proxy_params;
        }
        
        # 订单服务路由
        location ~ ^/api/v1/orders {
            limit_req zone=api_limit burst=20 nodelay;
            proxy_pass http://order-service;
            include /etc/nginx/proxy_params;
        }
        
        # 通知服务路由
        location ~ ^/api/v1/(notifications|templates) {
            limit_req zone=api_limit burst=10 nodelay;
            proxy_pass http://notification-service;
            include /etc/nginx/proxy_params;
        }
        
        # 健康检查路由
        location /health/user {
            proxy_pass http://user-service/health;
            include /etc/nginx/proxy_params;
        }
        
        location /health/product {
            proxy_pass http://product-service/health;
            include /etc/nginx/proxy_params;
        }
        
        location /health/order {
            proxy_pass http://order-service/health;
            include /etc/nginx/proxy_params;
        }
        
        location /health/notification {
            proxy_pass http://notification-service/health;
            include /etc/nginx/proxy_params;
        }
        
        # 统计接口路由
        location /stats/user {
            proxy_pass http://user-service/api/v1/stats;
            include /etc/nginx/proxy_params;
        }
        
        location /stats/product {
            proxy_pass http://product-service/api/v1/stats;
            include /etc/nginx/proxy_params;
        }
        
        location /stats/order {
            proxy_pass http://order-service/api/v1/stats;
            include /etc/nginx/proxy_params;
        }
        
        location /stats/notification {
            proxy_pass http://notification-service/api/v1/stats;
            include /etc/nginx/proxy_params;
        }
        
        # 静态文件服务
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri $uri/ =404;
        }
        
        # 上传文件服务
        location /uploads/ {
            alias /var/uploads/;
            expires 30d;
            add_header Cache-Control "public";
            try_files $uri =404;
        }
        
        # 默认首页
        location / {
            return 200 "<!DOCTYPE html><html><head><title>电商微服务 - Kubernetes部署</title><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,sans-serif;margin:0;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#333}.container{max-width:1200px;margin:0 auto;padding:40px 20px}.header{text-align:center;color:white;margin-bottom:50px}.header h1{font-size:3em;margin:0;text-shadow:2px 2px 4px rgba(0,0,0,0.3)}.header p{font-size:1.2em;margin:10px 0;opacity:0.9}.services{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px;margin-bottom:40px}.service{background:white;padding:25px;border-radius:10px;box-shadow:0 4px 20px rgba(0,0,0,0.1);transition:transform 0.3s ease}.service:hover{transform:translateY(-5px)}.service h3{color:#5a67d8;margin:0 0 15px 0;font-size:1.4em}.endpoint{font-family:Monaco,Menlo,monospace;background:#f7fafc;padding:8px 12px;margin:8px 0;border-radius:5px;font-size:0.9em;border-left:3px solid #5a67d8}.health-link{color:#38a169;text-decoration:none;font-weight:500}.health-link:hover{text-decoration:underline}.status-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin-top:30px}.status-card{background:rgba(255,255,255,0.1);padding:20px;border-radius:8px;text-align:center;color:white}.status-card h4{margin:0 0 10px 0;font-size:1.1em}.status-indicator{width:12px;height:12px;border-radius:50%;display:inline-block;margin-right:8px}.status-healthy{background:#48bb78}.footer{text-align:center;margin-top:40px;color:white;opacity:0.8}</style></head><body><div class=\"container\"><div class=\"header\"><h1>🛒 电商微服务平台</h1><p>Kubernetes云原生部署 | 高可用微服务架构</p></div><div class=\"services\"><div class=\"service\"><h3><span class=\"status-indicator status-healthy\"></span>👤 用户服务</h3><div class=\"endpoint\">POST /api/v1/register - 用户注册</div><div class=\"endpoint\">POST /api/v1/login - 用户登录</div><div class=\"endpoint\">GET /api/v1/profile - 个人信息</div><div class=\"endpoint\">PUT /api/v1/profile - 更新信息</div><p>健康检查: <a href=\"/health/user\" class=\"health-link\">/health/user</a></p></div><div class=\"service\"><h3><span class=\"status-indicator status-healthy\"></span>📦 商品服务</h3><div class=\"endpoint\">GET /api/v1/products - 商品列表</div><div class=\"endpoint\">GET /api/v1/categories - 分类列表</div><div class=\"endpoint\">GET /api/v1/products/search - 商品搜索</div><div class=\"endpoint\">POST /api/v1/products - 添加商品</div><p>健康检查: <a href=\"/health/product\" class=\"health-link\">/health/product</a></p></div><div class=\"service\"><h3><span class=\"status-indicator status-healthy\"></span>📋 订单服务</h3><div class=\"endpoint\">POST /api/v1/orders - 创建订单</div><div class=\"endpoint\">GET /api/v1/orders - 订单列表</div><div class=\"endpoint\">POST /api/v1/orders/{id}/pay - 支付订单</div><div class=\"endpoint\">PUT /api/v1/orders/{id}/cancel - 取消订单</div><p>健康检查: <a href=\"/health/order\" class=\"health-link\">/health/order</a></p></div><div class=\"service\"><h3><span class=\"status-indicator status-healthy\"></span>📬 通知服务</h3><div class=\"endpoint\">POST /api/v1/notifications - 发送通知</div><div class=\"endpoint\">GET /api/v1/notifications - 通知历史</div><div class=\"endpoint\">GET /api/v1/templates - 通知模板</div><div class=\"endpoint\">POST /api/v1/templates - 创建模板</div><p>健康检查: <a href=\"/health/notification\" class=\"health-link\">/health/notification</a></p></div></div><div class=\"status-grid\"><div class=\"status-card\"><h4>系统状态</h4><p><a href=\"/health\" style=\"color: white;\">网关健康检查</a></p></div><div class=\"status-card\"><h4>服务统计</h4><p><a href=\"/stats/user\" style=\"color: white;\">用户统计</a></p></div><div class=\"status-card\"><h4>商品统计</h4><p><a href=\"/stats/product\" style=\"color: white;\">商品统计</a></p></div><div class=\"status-card\"><h4>订单统计</h4><p><a href=\"/stats/order\" style=\"color: white;\">订单统计</a></p></div></div><div class=\"footer\"><p><strong>架构特性:</strong> 微服务 | 容器化 | Kubernetes | 高可用 | 负载均衡</p><p>版本: v1.0.0 | 部署时间: 2024-01-01 | 环境: Production</p></div></div></body></html>";
            add_header Content-Type text/html;
        }
        
        # 错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            return 404 '{"error": "Resource not found", "status": 404, "timestamp": "$time_iso8601"}';
            add_header Content-Type application/json;
        }
        
        location = /50x.html {
            return 500 '{"error": "Internal server error", "status": 500, "timestamp": "$time_iso8601"}';
            add_header Content-Type application/json;
        }
    }
  
  proxy_params: |
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # 连接和超时设置
    proxy_connect_timeout 30s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 缓冲设置
    proxy_buffering on;
    proxy_buffer_size 8k;
    proxy_buffers 16 8k;
    proxy_busy_buffers_size 16k;
    
    # 错误处理
    proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    proxy_next_upstream_tries 3;
    proxy_next_upstream_timeout 30s;
    
    # 保持连接
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    
    # 禁用代理重定向
    proxy_redirect off;