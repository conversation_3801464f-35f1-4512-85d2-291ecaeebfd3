# 用户服务部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: ecommerce-k8s
  labels:
    app: user-service
    tier: backend
    component: microservice
    service: user
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: user-service
        tier: backend
        component: microservice
        service: user
        version: v1.0
    spec:
      containers:
      - name: user-service
        image: ecommerce-basic-user-service:latest
        imagePullPolicy: Never  # 使用本地镜像
        ports:
        - containerPort: 5001
          name: http
          protocol: TCP
        env:
        # 数据库配置
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: user_database_url
        # Redis配置
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: redis_user_url
        # RabbitMQ配置
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: rabbitmq_url
        # 应用配置
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: flask_env
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: log_level
        # 密钥配置
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt_secret
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: api_key
        # 服务间通信
        - name: PRODUCT_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: product_service_url
        - name: ORDER_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: order_service_url
        - name: NOTIFICATION_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: notification_service_url
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          httpGet:
            path: /health
            port: 5001
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 5001
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 5001
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 10
        volumeMounts:
        - name: app-logs
          mountPath: /var/log/app
        - name: uploads
          mountPath: /var/uploads
      volumes:
      - name: app-logs
        emptyDir: {}
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: ecommerce-k8s
  labels:
    app: user-service
    tier: backend
    service: user
spec:
  selector:
    app: user-service
  ports:
  - name: http
    port: 80
    targetPort: 5001
    protocol: TCP
  type: ClusterIP
  sessionAffinity: None
---
# 用户服务HPA配置（水平自动扩缩容）
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: ecommerce-k8s
  labels:
    app: user-service
    tier: backend
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
---
# 用户服务PodDisruptionBudget配置（Pod中断预算）
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: user-service-pdb
  namespace: ecommerce-k8s
  labels:
    app: user-service
    tier: backend
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: user-service
---
# 用户服务网络策略（可选）
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: user-service-netpol
  namespace: ecommerce-k8s
  labels:
    app: user-service
    tier: backend
spec:
  podSelector:
    matchLabels:
      app: user-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    - podSelector:
        matchLabels:
          tier: backend
    ports:
    - protocol: TCP
      port: 5001
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: rabbitmq
    ports:
    - protocol: TCP
      port: 5672
  - to:
    - podSelector:
        matchLabels:
          tier: backend
    ports:
    - protocol: TCP
      port: 80
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53