# HTTPBin 服务授权策略
# 演示基于不同条件的访问控制

---
# 允许 sleep 服务访问 httpbin
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: httpbin-viewer
  namespace: default
spec:
  selector:
    matchLabels:
      app: httpbin
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/sleep"]
  - to:
    - operation:
        methods: ["GET", "POST"]

---
# 基于路径的访问控制
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: httpbin-path-based
  namespace: default
spec:
  selector:
    matchLabels:
      app: httpbin
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/sleep"]
  - to:
    - operation:
        methods: ["GET"]
        paths: ["/ip", "/headers", "/status/*"]
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/sleep"]
  - to:
    - operation:
        methods: ["POST"]
        paths: ["/post"]

---
# 基于时间的访问控制（示例）
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: httpbin-time-based
  namespace: default
spec:
  selector:
    matchLabels:
      app: httpbin
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/sleep"]
  - to:
    - operation:
        methods: ["GET"]
  - when:
    - key: request.time
      values: ["09:00:00", "17:00:00"]
