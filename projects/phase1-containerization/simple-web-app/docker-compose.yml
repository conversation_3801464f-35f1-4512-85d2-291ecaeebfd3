version: '3.8'

services:
  # Flask Web应用服务
  web:
    build:
      context: .
      dockerfile: Dockerfile
    image: simple-web-app:latest
    container_name: simple-web-app
    restart: unless-stopped
    ports:
      - "8080:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      - SECRET_KEY=your-secret-key-change-in-production
      - DOCKER_IMAGE=simple-web-app:latest
      - BUILD_DATE=${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
    volumes:
      # 可选：挂载日志目录
      - ./logs:/app/logs
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - nginx

  # Nginx反向代理服务
  nginx:
    image: nginx:alpine
    container_name: simple-web-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge
    name: simple-web-network

volumes:
  logs:
    driver: local