# ==============================================================================
# 电商应用基础版 .gitignore
# ==============================================================================

# 环境变量和配置
.env
.env.local
.env.production

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 依赖目录
node_modules/

# Docker 相关
.dockerignore

# 数据目录（运行时生成）
data/postgres/
data/redis/
data/rabbitmq/

# 备份文件
database/backups/*.sql
*.backup

# 临时文件
.tmp/
temp/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 密钥和证书
*.key
*.pem
*.crt
secrets/

# Coverage 报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json