# ==============================================================================
# 电商应用基础版 - 环境变量配置示例
# 复制此文件为 .env 并设置实际的配置值
# ==============================================================================

# 项目基础配置
PROJECT_NAME=ecommerce-basic
ENVIRONMENT=production

# 数据库配置
DB_PASSWORD=ecommerce123
DB_PORT=5432
DB_NAME=ecommerce
DB_USER=postgres

# Redis 配置
REDIS_PASSWORD=redis123
REDIS_PORT=6379

# RabbitMQ 配置
RABBITMQ_PASSWORD=rabbitmq123
RABBITMQ_PORT=5672
RABBITMQ_MGMT_PORT=15672

# Nginx 配置
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# JWT 密钥配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# 邮件服务配置
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# 短信服务配置
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret

# 支付网关配置
PAYMENT_GATEWAY_URL=https://api.stripe.com
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret

# Elasticsearch 配置（可选）
ELASTICSEARCH_URL=http://elasticsearch:9200

# 监控配置（可选）
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# 安全配置
ALLOWED_ORIGINS=http://localhost,http://127.0.0.1
CORS_ENABLED=true

# 日志级别
LOG_LEVEL=INFO

# ==============================================================================
# 注意事项：
# 1. 生产环境中请务必修改所有默认密码
# 2. JWT_SECRET 应使用强随机字符串
# 3. 数据库密码应包含大小写字母、数字和特殊字符
# 4. 不要将 .env 文件提交到版本控制系统
# ==============================================================================