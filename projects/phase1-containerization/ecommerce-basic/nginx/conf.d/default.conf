# ==============================================================================
# 电商应用基础版 - Nginx 站点配置
# API网关和静态文件服务配置
# ==============================================================================

server {
    listen 80;
    server_name localhost;
    
    # 访问日志
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 静态文件服务
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri $uri/ =404;
    }

    # API路由 - 用户服务
    location /api/v1/register {
        proxy_pass http://user-service;
        include /etc/nginx/proxy_params;
    }

    location /api/v1/login {
        proxy_pass http://user-service;
        include /etc/nginx/proxy_params;
    }

    location /api/v1/profile {
        proxy_pass http://user-service;
        include /etc/nginx/proxy_params;
    }

    location /api/v1/logout {
        proxy_pass http://user-service;
        include /etc/nginx/proxy_params;
    }

    location ~ ^/api/v1/users {
        proxy_pass http://user-service;
        include /etc/nginx/proxy_params;
    }

    # API路由 - 商品服务
    location ~ ^/api/v1/(products|categories) {
        proxy_pass http://product-service;
        include /etc/nginx/proxy_params;
    }

    # API路由 - 订单服务
    location ~ ^/api/v1/orders {
        proxy_pass http://order-service;
        include /etc/nginx/proxy_params;
    }

    # API路由 - 通知服务
    location ~ ^/api/v1/(notifications|templates) {
        proxy_pass http://notification-service;
        include /etc/nginx/proxy_params;
    }

    # 服务健康检查代理
    location /health/user {
        proxy_pass http://user-service/health;
        include /etc/nginx/proxy_params;
    }

    location /health/product {
        proxy_pass http://product-service/health;
        include /etc/nginx/proxy_params;
    }

    location /health/order {
        proxy_pass http://order-service/health;
        include /etc/nginx/proxy_params;
    }

    location /health/notification {
        proxy_pass http://notification-service/health;
        include /etc/nginx/proxy_params;
    }

    # 服务统计信息代理
    location /stats/user {
        proxy_pass http://user-service/api/v1/stats;
        include /etc/nginx/proxy_params;
    }

    location /stats/product {
        proxy_pass http://product-service/api/v1/stats;
        include /etc/nginx/proxy_params;
    }

    location /stats/order {
        proxy_pass http://order-service/api/v1/stats;
        include /etc/nginx/proxy_params;
    }

    location /stats/notification {
        proxy_pass http://notification-service/api/v1/stats;
        include /etc/nginx/proxy_params;
    }

    # 默认首页
    location / {
        return 200 '
<!DOCTYPE html>
<html>
<head>
    <title>电商应用基础版</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .service { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .service h3 { margin: 0 0 10px 0; color: #2c5aa0; }
        .endpoint { font-family: monospace; background: #f8f8f8; padding: 5px; margin: 5px 0; border-radius: 3px; }
        .status { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px; }
        .status.healthy { background: #28a745; }
        .footer { text-align: center; margin-top: 30px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 电商应用基础版</h1>
        <p>欢迎使用基于微服务架构的电商应用！</p>
        
        <div class="service">
            <h3><span class="status healthy"></span>用户服务 (User Service)</h3>
            <div class="endpoint">POST /api/v1/register - 用户注册</div>
            <div class="endpoint">POST /api/v1/login - 用户登录</div>
            <div class="endpoint">GET /api/v1/profile - 获取用户信息</div>
            <div class="endpoint">健康检查: <a href="/health/user">/health/user</a></div>
        </div>
        
        <div class="service">
            <h3><span class="status healthy"></span>商品服务 (Product Service)</h3>
            <div class="endpoint">GET /api/v1/products - 获取商品列表</div>
            <div class="endpoint">GET /api/v1/categories - 获取分类列表</div>
            <div class="endpoint">GET /api/v1/products/search - 商品搜索</div>
            <div class="endpoint">健康检查: <a href="/health/product">/health/product</a></div>
        </div>
        
        <div class="service">
            <h3><span class="status healthy"></span>订单服务 (Order Service)</h3>
            <div class="endpoint">POST /api/v1/orders - 创建订单</div>
            <div class="endpoint">GET /api/v1/orders - 获取订单列表</div>
            <div class="endpoint">POST /api/v1/orders/{id}/pay - 支付订单</div>
            <div class="endpoint">健康检查: <a href="/health/order">/health/order</a></div>
        </div>
        
        <div class="service">
            <h3><span class="status healthy"></span>通知服务 (Notification Service)</h3>
            <div class="endpoint">POST /api/v1/notifications - 发送通知</div>
            <div class="endpoint">GET /api/v1/notifications - 获取通知历史</div>
            <div class="endpoint">GET /api/v1/templates - 获取通知模板</div>
            <div class="endpoint">健康检查: <a href="/health/notification">/health/notification</a></div>
        </div>
        
        <div class="footer">
            <p><strong>系统监控:</strong></p>
            <p>
                <a href="/stats/user">用户统计</a> | 
                <a href="/stats/product">商品统计</a> | 
                <a href="/stats/order">订单统计</a> | 
                <a href="/stats/notification">通知统计</a>
            </p>
            <p>版本: 1.0.0 | 构建时间: 2024-01-01</p>
        </div>
    </div>
</body>
</html>
        ';
        add_header Content-Type text/html;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        return 404 '{"error": "Resource not found", "status": 404}';
        add_header Content-Type application/json;
    }
    
    location = /50x.html {
        return 500 '{"error": "Internal server error", "status": 500}';
        add_header Content-Type application/json;
    }
}