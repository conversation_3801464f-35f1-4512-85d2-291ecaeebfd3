# ==============================================================================
# 电商应用基础版 - Nginx 主配置文件
# 负载均衡和反向代理配置
# ==============================================================================

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 上游服务器定义
    upstream user-service {
        least_conn;
        server user-service:5001 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream product-service {
        least_conn;
        server product-service:5002 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream order-service {
        least_conn;
        server order-service:5003 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream notification-service {
        least_conn;
        server notification-service:5004 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}