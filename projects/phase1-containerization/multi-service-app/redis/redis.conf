# ==============================================================================
# Redis配置文件
# Todo List Plus项目Redis服务器配置
# ==============================================================================

# 网络配置
bind 0.0.0.0
port 6379
protected-mode yes
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 通用配置
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 持久化配置
# RDB持久化
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# AOF持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# 内存管理
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 惰性释放
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# 威胁预防
# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_5f4dcc3b5aa765d61d8327deb882cf99"
rename-command DEBUG ""
rename-command EVAL ""
rename-command SHUTDOWN "SHUTDOWN_5f4dcc3b5aa765d61d8327deb882cf99"

# 客户端配置
maxclients 10000
timeout 0

# 安全配置
# 密码认证（通过环境变量设置）
# requirepass your_redis_password

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100

# 事件通知
notify-keyspace-events "Ex"

# 高级配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# HyperLogLog
hll-sparse-max-bytes 3000

# Streams
stream-node-max-bytes 4096
stream-node-max-entries 100

# TLS配置（如果需要）
# port 0
# tls-port 6380
# tls-cert-file redis.crt
# tls-key-file redis.key
# tls-ca-cert-file ca.crt

# 模块配置
# loadmodule /path/to/module.so

# 包含其他配置文件
# include /path/to/local.conf
# include /path/to/other.conf

# 集群配置（如果需要集群模式）
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-announce-ip ***********
# cluster-announce-port 6379
# cluster-announce-bus-port 16379

# 哨兵配置（如果需要哨兵模式）
# sentinel monitor mymaster 127.0.0.1 6379 2
# sentinel down-after-milliseconds mymaster 30000
# sentinel parallel-syncs mymaster 1
# sentinel failover-timeout mymaster 180000

# 调试和开发配置
# always-show-logo yes