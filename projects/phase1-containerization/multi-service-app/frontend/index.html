<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Todo List Plus - 云原生任务管理系统</title>
  <meta name="description" content="基于Vue.js 3和FastAPI的现代化任务管理系统" />
  <meta name="keywords" content="任务管理,Vue.js,FastAPI,云原生,Docker" />
  <meta name="author" content="Todo List Plus Team" />
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="Todo List Plus - 云原生任务管理系统" />
  <meta property="og:description" content="基于Vue.js 3和FastAPI的现代化任务管理系统" />
  <meta property="og:type" content="website" />
  
  <!-- 预加载关键资源 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- 初始样式，防止闪烁 -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background-color: #f2f3f5;
      color: #303133;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    #app {
      min-height: 100vh;
    }
    
    /* 加载动画 */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.3s ease;
    }
    
    .loading-logo {
      font-size: 48px;
      color: #409eff;
      margin-bottom: 20px;
    }
    
    .loading-text {
      font-size: 16px;
      color: #606266;
      margin-bottom: 20px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 深色主题初始样式 */
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #0a0a0a;
        color: #e5eaf3;
      }
      
      .loading-container {
        background: #141414;
      }
      
      .loading-text {
        color: #cfd3dc;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 初始加载动画 -->
    <div id="initial-loading" class="loading-container">
      <div class="loading-logo">📋</div>
      <div class="loading-text">Todo List Plus</div>
      <div class="loading-text">正在加载应用...</div>
      <div class="loading-spinner"></div>
    </div>
  </div>
  
  <script type="module" src="/src/main.ts"></script>
  
  <script>
    // 移除初始加载动画
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loading = document.getElementById('initial-loading');
        if (loading) {
          loading.style.opacity = '0';
          setTimeout(() => {
            loading.remove();
          }, 300);
        }
      }, 500);
    });
    
    // 应用加载错误处理
    window.addEventListener('error', (event) => {
      console.error('Application load error:', event.error);
    });
    
    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
    });
  </script>
</body>
</html>