@import './variables.scss';

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px;
  line-height: 1.6;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  color: var(--text-primary);
  background-color: var(--bg-color-page);
  transition: background-color $transition-duration $transition-function;
}

#app {
  min-height: 100vh;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--border-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 3px;

  &:hover {
    background: var(--text-placeholder);
  }
}

// 通用工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

// 响应式工具类
.hidden-xs {
  @media (max-width: #{$mobile - 1px}) {
    display: none !important;
  }
}

.hidden-sm {
  @media (min-width: #{$mobile}) and (max-width: #{$tablet - 1px}) {
    display: none !important;
  }
}

.hidden-md {
  @media (min-width: #{$tablet}) and (max-width: #{$desktop - 1px}) {
    display: none !important;
  }
}

.hidden-lg {
  @media (min-width: #{$desktop}) {
    display: none !important;
  }
}

// 自定义Element Plus样式覆盖
.el-menu {
  border: none !important;
}

.el-menu-item {
  transition: all $transition-duration $transition-function;
}

.el-button {
  transition: all $transition-duration $transition-function;
}

.el-card {
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
  transition: all $transition-duration $transition-function;

  &:hover {
    box-shadow: var(--shadow-base);
  }
}

.el-form-item__label {
  color: var(--text-regular) !important;
}

// 页面布局样式
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-light);

  .title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
  }

  .subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: 4px;
  }
}

.content-card {
  background: var(--bg-color);
  border-radius: 8px;
  box-shadow: var(--shadow-light);
  padding: 20px;
  margin-bottom: 20px;
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-duration $transition-function;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all $transition-duration $transition-function;
}

.slide-fade-leave-active {
  transition: all $transition-duration $transition-function;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

// 加载状态
.loading-container {
  position: relative;
  min-height: 200px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

[data-theme='dark'] .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.8);
}

// NProgress样式自定义
#nprogress .bar {
  background: var(--primary-color) !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px var(--primary-color), 0 0 5px var(--primary-color) !important;
}