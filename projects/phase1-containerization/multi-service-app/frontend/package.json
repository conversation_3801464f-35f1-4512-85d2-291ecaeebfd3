{"name": "todo-list-plus-frontend", "version": "1.0.0", "description": "Todo List Plus - 云原生任务管理系统前端", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext vue,js,jsx,cjs,mjs,ts,tsx,cts,mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "^10.4.1", "axios": "^1.5.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "element-plus": "^2.3.14", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.6", "socket.io-client": "^4.7.2", "sortablejs": "^1.15.0", "vue": "^3.3.4", "vue-draggable-plus": "^0.2.4", "vue-echarts": "^6.6.1", "vue-i18n": "^9.4.1", "vue-router": "^4.2.4"}, "devDependencies": {"@iconify/json": "^2.2.121", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.8", "@types/node": "^20.5.9", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.3.4", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.48.0", "eslint-plugin-vue": "^9.17.0", "happy-dom": "^10.10.4", "jsdom": "^22.1.0", "prettier": "^3.0.3", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.66.1", "typescript": "^5.2.2", "unplugin-auto-import": "^0.16.6", "unplugin-icons": "^0.17.0", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.9", "vitest": "^0.34.6", "vue-tsc": "^1.8.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}