# ==============================================================================
# 环境变量模板文件
# 复制此文件为 .env 并修改相应的值
# ==============================================================================

# 项目配置
PROJECT_NAME=todo-list-plus
NODE_ENV=development
DEBUG=true

# 数据库配置
DB_NAME=todo_db
DB_USER=postgres
DB_PASSWORD=postgres123
DB_HOST=database
DB_PORT=5432

# Redis配置
REDIS_PASSWORD=redis123
REDIS_HOST=redis
REDIS_PORT=6379

# API配置
API_URL=http://localhost:8000
FRONTEND_URL=http://localhost:3000

# 安全配置
SECRET_KEY=your-super-secret-jwt-key-change-in-production
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 端口配置
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443
BACKEND_PORT=8000
FRONTEND_PORT=3000

# 日志级别
LOG_LEVEL=INFO

# 开发相关
HOT_RELOAD=true