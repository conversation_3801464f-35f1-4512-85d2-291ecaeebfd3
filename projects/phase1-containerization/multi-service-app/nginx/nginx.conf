# ==============================================================================
# Nginx主配置文件
# 为Todo List Plus多服务应用提供反向代理和负载均衡
# ==============================================================================

# 运行用户
user nginx;

# 工作进程数（自动检测CPU核心数）
worker_processes auto;

# 错误日志
error_log /var/log/nginx/error.log warn;

# 进程文件
pid /var/run/nginx.pid;

# 事件模块
events {
    # 每个工作进程的最大连接数
    worker_connections 1024;
    
    # 使用epoll事件模型（Linux推荐）
    use epoll;
    
    # 允许一个工作进程同时接受多个新连接
    multi_accept on;
}

# HTTP模块
http {
    # MIME类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';
    
    # JSON格式日志（用于日志分析）
    log_format json_combined escape=json
    '{'
        '"time_local":"$time_local",'
        '"remote_addr":"$remote_addr",'
        '"remote_user":"$remote_user",'
        '"request":"$request",'
        '"status": "$status",'
        '"body_bytes_sent":"$body_bytes_sent",'
        '"request_time":"$request_time",'
        '"http_referrer":"$http_referer",'
        '"http_user_agent":"$http_user_agent",'
        '"http_x_real_ip":"$http_x_real_ip",'
        '"http_x_forwarded_for":"$http_x_forwarded_for",'
        '"http_x_forwarded_proto":"$http_x_forwarded_proto",'
        '"upstream_addr":"$upstream_addr",'
        '"upstream_status":"$upstream_status",'
        '"upstream_response_time":"$upstream_response_time"'
    '}';
    
    # 访问日志
    access_log /var/log/nginx/access.log json_combined;
    
    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # 缓冲区大小
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # 超时设置
    client_header_timeout 3m;
    client_body_timeout 3m;
    send_timeout 3m;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:;";
    
    # 速率限制
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # 上游服务器定义
    upstream backend_servers {
        # 后端API服务器
        server backend:8000 max_fails=3 fail_timeout=30s;
        
        # 负载均衡策略
        # ip_hash; # 基于IP的会话保持
        # least_conn; # 最少连接数
        
        # 健康检查
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }
    
    upstream frontend_servers {
        # 前端服务器
        server frontend:3000 max_fails=3 fail_timeout=30s;
        
        # 健康检查
        keepalive 16;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }
    
    # 缓存配置
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=100m inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=500m inactive=1d use_temp_path=off;
    
    # 创建缓存目录
    proxy_temp_path /var/cache/nginx/temp;
    
    # 包含其他配置文件
    include /etc/nginx/conf.d/*.conf;
}