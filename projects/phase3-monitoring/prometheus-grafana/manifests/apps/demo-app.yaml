# Demo App 部署清单
# 用于演示 Prometheus 监控功能的示例应用

---
# Demo App Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-app
  namespace: monitoring
  labels:
    app: demo-app
    version: v1.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: demo-app
  template:
    metadata:
      labels:
        app: demo-app
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "5000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
        - name: demo-app
          image: demo-app:latest
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 5000
              protocol: TCP
          env:
            - name: FLASK_ENV
              value: "production"
            - name: PYTHONUNBUFFERED
              value: "1"
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"

---
# Demo App Service
apiVersion: v1
kind: Service
metadata:
  name: demo-app
  namespace: monitoring
  labels:
    app: demo-app
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "5000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 80
      targetPort: http
      protocol: TCP
  selector:
    app: demo-app

---
# Demo App Ingress (可选)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: demo-app
  namespace: monitoring
  labels:
    app: demo-app
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
    - host: demo-app.local
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: demo-app
                port:
                  number: 80

---
# Demo App ConfigMap (可选配置)
apiVersion: v1
kind: ConfigMap
metadata:
  name: demo-app-config
  namespace: monitoring
  labels:
    app: demo-app
data:
  app.conf: |
    # Demo App Configuration
    DEBUG=false
    LOG_LEVEL=INFO
    METRICS_ENABLED=true
    HEALTH_CHECK_INTERVAL=30
