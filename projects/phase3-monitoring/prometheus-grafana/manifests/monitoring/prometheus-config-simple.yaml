apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config-simple
  namespace: monitoring
  labels:
    app: prometheus
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "/etc/prometheus/rules/*.yml"

    scrape_configs:
      # Prometheus 自身监控
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      # Demo App 静态配置 (使用 Pod IP 和正确端口)
      - job_name: 'demo-app'
        static_configs:
          - targets: ['***********:5000', '***********:5000']
        metrics_path: '/metrics'
        scrape_interval: 15s
