# 云原生日志收集与分析项目文档

## 📚 文档目录

本目录包含了云原生可观测性系统的完整文档，按照类型和用途进行了分类整理。

### 📖 使用指南 (guides/)

实用的操作指南和使用说明：

- **[脚本使用指南](guides/scripts-usage-guide.md)** - 详细的脚本使用方法和示例
- **[端口转发指南](guides/port-forward-guide.md)** - 端口转发管理和使用说明
- **[DNS解决方案指南](guides/dns-solution-guide.md)** - DNS问题分析和解决方案

### 📊 项目报告 (reports/)

项目执行过程中的各种报告和总结：

- **[验证报告](reports/verification-report.md)** - 系统功能验证和测试结果
- **[调试总结](reports/debugging-summary.md)** - 问题调试过程和解决方案
- **[项目完成总结](reports/project-completion-summary.md)** - 项目整体完成情况和成果

### 📋 技术参考 (references/)

技术架构和详细参考文档：

- **[系统架构](references/architecture.md)** - 系统架构设计和组件说明
- **[部署指南](references/deployment-guide.md)** - 详细的部署步骤和配置
- **[故障排查](references/troubleshooting.md)** - 常见问题和解决方法

## 🚀 快速开始

### 新用户推荐阅读顺序

1. **[项目主页](../README.md)** - 项目概览和快速开始
2. **[脚本使用指南](guides/scripts-usage-guide.md)** - 了解如何使用项目脚本
3. **[端口转发指南](guides/port-forward-guide.md)** - 学习如何访问服务
4. **[系统架构](references/architecture.md)** - 理解系统设计
5. **[验证报告](reports/verification-report.md)** - 查看系统验证结果

### 问题解决

遇到问题时的查阅顺序：

1. **[DNS解决方案指南](guides/dns-solution-guide.md)** - DNS相关问题
2. **[故障排查](references/troubleshooting.md)** - 常见问题解决
3. **[调试总结](reports/debugging-summary.md)** - 历史问题和解决方案

## 📝 文档维护

### 文档分类标准

- **guides/** - 面向用户的操作指南，重点在"如何做"
- **reports/** - 项目执行报告，记录过程和结果
- **references/** - 技术参考文档，提供详细的技术信息

### 文档命名规范

- 使用英文文件名，便于版本控制和跨平台兼容
- 使用连字符分隔单词 (kebab-case)
- 文件名应该清楚表达文档内容

### 更新说明

所有文档都会根据项目进展持续更新，确保信息的准确性和时效性。

## 🔗 相关链接

- **[项目主页](../README.md)** - 返回项目主页
- **[脚本目录](../scripts/)** - 查看所有管理脚本
- **[配置文件](../manifests/)** - Kubernetes 配置文件
- **[应用源码](../apps/)** - 示例应用源代码

---

📅 **最后更新**: 2025年6月27日  
📧 **维护者**: 云原生学习项目团队
