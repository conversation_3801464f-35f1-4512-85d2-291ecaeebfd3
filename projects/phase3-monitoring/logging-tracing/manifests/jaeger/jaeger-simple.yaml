apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
  namespace: tracing
  labels:
    app: jaeger
    component: all-in-one
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jaeger
      component: all-in-one
  template:
    metadata:
      labels:
        app: jaeger
        component: all-in-one
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "16686"
    spec:
      containers:
      - name: jaeger
        image: jaegertracing/all-in-one:1.51.0
        args:
          - --memory.max-traces=10000
          - --collector.zipkin.host-port=:9411
          - --collector.grpc-server.host-port=:14250
          - --collector.http-server.host-port=:14268
          - --query.base-path=/
          - --log-level=info
        env:
        - name: SPAN_STORAGE_TYPE
          value: memory
        - name: COLLECTOR_ZIPKIN_HOST_PORT
          value: ":9411"
        - name: COLLECTOR_GRPC_SERVER_HOST_PORT
          value: ":14250"
        - name: COLLECTOR_HTTP_SERVER_HOST_PORT
          value: ":14268"
        - name: QUERY_BASE_PATH
          value: /
        ports:
        - containerPort: 5775
          protocol: UDP
          name: zk-compact-trft
        - containerPort: 6831
          protocol: UDP
          name: jg-compact-trft
        - containerPort: 6832
          protocol: UDP
          name: jg-binary-trft
        - containerPort: 5778
          protocol: TCP
          name: agent-configs
        - containerPort: 16686
          protocol: TCP
          name: query
        - containerPort: 9411
          protocol: TCP
          name: zipkin
        - containerPort: 14250
          protocol: TCP
          name: grpc
        - containerPort: 14268
          protocol: TCP
          name: http
        - containerPort: 14269
          protocol: TCP
          name: admin
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 200m
            memory: 512Mi
        readinessProbe:
          httpGet:
            path: /
            port: 16686
          initialDelaySeconds: 30
          timeoutSeconds: 10
        livenessProbe:
          httpGet:
            path: /
            port: 16686
          initialDelaySeconds: 60
          timeoutSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query
  namespace: tracing
  labels:
    app: jaeger
    component: query
spec:
  ports:
  - name: query-http
    port: 16686
    protocol: TCP
    targetPort: 16686
  selector:
    app: jaeger
    component: all-in-one
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-collector
  namespace: tracing
  labels:
    app: jaeger
    component: collector
spec:
  ports:
  - name: jaeger-collector-grpc
    port: 14250
    protocol: TCP
    targetPort: 14250
  - name: jaeger-collector-http
    port: 14268
    protocol: TCP
    targetPort: 14268
  - name: jaeger-collector-zipkin
    port: 9411
    protocol: TCP
    targetPort: 9411
  selector:
    app: jaeger
    component: all-in-one
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-agent
  namespace: tracing
  labels:
    app: jaeger
    component: agent
spec:
  ports:
  - name: agent-zipkin-thrift
    port: 5775
    protocol: UDP
    targetPort: 5775
  - name: agent-compact
    port: 6831
    protocol: UDP
    targetPort: 6831
  - name: agent-binary
    port: 6832
    protocol: UDP
    targetPort: 6832
  - name: agent-configs
    port: 5778
    protocol: TCP
    targetPort: 5778
  selector:
    app: jaeger
    component: all-in-one
  clusterIP: None
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query-nodeport
  namespace: tracing
  labels:
    app: jaeger
    component: query
spec:
  type: NodePort
  ports:
  - name: query-http
    port: 16686
    protocol: TCP
    targetPort: 16686
    nodePort: 30686
  selector:
    app: jaeger
    component: all-in-one
