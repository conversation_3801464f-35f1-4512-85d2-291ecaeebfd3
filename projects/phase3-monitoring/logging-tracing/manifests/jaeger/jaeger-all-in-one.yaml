apiVersion: v1
kind: ConfigMap
metadata:
  name: jaeger-config
  namespace: tracing
  labels:
    app: jaeger
data:
  jaeger-config.yaml: |
    # Jaeger 配置文件
    # 使用 Elasticsearch 作为存储后端
    
    # 采样配置
    sampling:
      default_strategy:
        type: probabilistic
        param: 0.1  # 10% 采样率
      per_service_strategies:
        - service: "user-service"
          type: probabilistic
          param: 1.0  # 用户服务 100% 采样
        - service: "order-service"
          type: probabilistic
          param: 0.5  # 订单服务 50% 采样
        - service: "payment-service"
          type: probabilistic
          param: 1.0  # 支付服务 100% 采样
    
    # 存储配置
    storage:
      type: elasticsearch
      elasticsearch:
        server-urls: http://***********:9200
        index-prefix: jaeger
        username: ""
        password: ""
        sniffer: false
        max-span-age: 168h  # 7 天
        num-shards: 5
        num-replicas: 1
        bulk:
          size: 5000000  # 5MB
          workers: 1
          actions: 1000
          flush-interval: 200ms
    
    # 查询配置
    query:
      base-path: /
      static-files: /go/jaeger-ui/
      ui-config: /etc/jaeger/ui-config.json
    
    # 收集器配置
    collector:
      zipkin:
        host-port: :9411
      grpc:
        host-port: :14250
      http:
        host-port: :14268
      queue:
        size: 2000
        workers: 50
      
  ui-config.json: |
    {
      "monitor": {
        "menuEnabled": true
      },
      "dependencies": {
        "menuEnabled": true
      },
      "archiveEnabled": true,
      "tracking": {
        "gaID": "",
        "trackErrors": true
      }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
  namespace: tracing
  labels:
    app: jaeger
    component: all-in-one
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jaeger
      component: all-in-one
  template:
    metadata:
      labels:
        app: jaeger
        component: all-in-one
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "16686"
    spec:
      containers:
      - name: jaeger
        image: jaegertracing/all-in-one:1.51.0
        args:
          - --es.server-urls=http://***********:9200
          - --es.index-prefix=jaeger
          - --es.num-shards=5
          - --es.num-replicas=1
          - --collector.zipkin.host-port=:9411
          - --collector.grpc-server.host-port=:14250
          - --collector.http-server.host-port=:14268
          - --query.base-path=/
          - --log-level=info
        env:
        - name: SPAN_STORAGE_TYPE
          value: elasticsearch
        - name: ES_SERVER_URLS
          value: http://***********:9200
        - name: ES_INDEX_PREFIX
          value: jaeger
        - name: COLLECTOR_ZIPKIN_HOST_PORT
          value: ":9411"
        - name: COLLECTOR_GRPC_SERVER_HOST_PORT
          value: ":14250"
        - name: COLLECTOR_HTTP_SERVER_HOST_PORT
          value: ":14268"
        - name: QUERY_BASE_PATH
          value: /
        ports:
        - containerPort: 5775
          protocol: UDP
          name: zk-compact-trft
        - containerPort: 6831
          protocol: UDP
          name: jg-compact-trft
        - containerPort: 6832
          protocol: UDP
          name: jg-binary-trft
        - containerPort: 5778
          protocol: TCP
          name: agent-configs
        - containerPort: 16686
          protocol: TCP
          name: query
        - containerPort: 9411
          protocol: TCP
          name: zipkin
        - containerPort: 14250
          protocol: TCP
          name: grpc
        - containerPort: 14268
          protocol: TCP
          name: http
        - containerPort: 14269
          protocol: TCP
          name: admin
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 200m
            memory: 512Mi
        volumeMounts:
        - name: jaeger-config
          mountPath: /etc/jaeger
        readinessProbe:
          httpGet:
            path: /
            port: 16686
          initialDelaySeconds: 30
          timeoutSeconds: 10
        livenessProbe:
          httpGet:
            path: /
            port: 16686
          initialDelaySeconds: 60
          timeoutSeconds: 10
      volumes:
      - name: jaeger-config
        configMap:
          name: jaeger-config
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query
  namespace: tracing
  labels:
    app: jaeger
    component: query
spec:
  ports:
  - name: query-http
    port: 16686
    protocol: TCP
    targetPort: 16686
  selector:
    app: jaeger
    component: all-in-one
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-collector
  namespace: tracing
  labels:
    app: jaeger
    component: collector
spec:
  ports:
  - name: jaeger-collector-grpc
    port: 14250
    protocol: TCP
    targetPort: 14250
  - name: jaeger-collector-http
    port: 14268
    protocol: TCP
    targetPort: 14268
  - name: jaeger-collector-zipkin
    port: 9411
    protocol: TCP
    targetPort: 9411
  selector:
    app: jaeger
    component: all-in-one
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-agent
  namespace: tracing
  labels:
    app: jaeger
    component: agent
spec:
  ports:
  - name: agent-zipkin-thrift
    port: 5775
    protocol: UDP
    targetPort: 5775
  - name: agent-compact
    port: 6831
    protocol: UDP
    targetPort: 6831
  - name: agent-binary
    port: 6832
    protocol: UDP
    targetPort: 6832
  - name: agent-configs
    port: 5778
    protocol: TCP
    targetPort: 5778
  selector:
    app: jaeger
    component: all-in-one
  clusterIP: None
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query-nodeport
  namespace: tracing
  labels:
    app: jaeger
    component: query
spec:
  type: NodePort
  ports:
  - name: query-http
    port: 16686
    protocol: TCP
    targetPort: 16686
    nodePort: 30686
  selector:
    app: jaeger
    component: all-in-one
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: jaeger-agent
  namespace: tracing
  labels:
    app: jaeger
    component: agent
spec:
  selector:
    matchLabels:
      app: jaeger
      component: agent
  template:
    metadata:
      labels:
        app: jaeger
        component: agent
    spec:
      containers:
      - name: jaeger-agent
        image: jaegertracing/jaeger-agent:1.51.0
        args:
          - --reporter.grpc.host-port=jaeger-collector.tracing.svc.cluster.local:14250
          - --log-level=info
        ports:
        - containerPort: 5775
          protocol: UDP
          name: zk-compact-trft
        - containerPort: 6831
          protocol: UDP
          name: jg-compact-trft
        - containerPort: 6832
          protocol: UDP
          name: jg-binary-trft
        - containerPort: 5778
          protocol: TCP
          name: configs
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
        env:
        - name: REPORTER_GRPC_HOST_PORT
          value: jaeger-collector.tracing.svc.cluster.local:14250
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
