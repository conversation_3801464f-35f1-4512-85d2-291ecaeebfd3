apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-config
  namespace: logging
  labels:
    app: kibana
data:
  kibana.yml: |
    server.name: kibana
    server.host: "0.0.0.0"
    server.port: 5601
    
    elasticsearch.hosts: ["http://***********:9200"]
    elasticsearch.pingTimeout: 1500
    elasticsearch.requestTimeout: 30000
    
    # 安全配置
    xpack.security.enabled: false
    xpack.encryptedSavedObjects.encryptionKey: "something_at_least_32_characters_long"
    
    # UI 配置
    i18n.locale: "en"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: logging
  labels:
    app: kibana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: docker.elastic.co/kibana/kibana:8.11.0
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi
        env:
        - name: ELASTICSEARCH_HOSTS
          value: "http://***********:9200"
        - name: SERVER_NAME
          value: "kibana"
        - name: SERVER_HOST
          value: "0.0.0.0"
        - name: XPACK_SECURITY_ENABLED
          value: "false"
        - name: XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY
          value: "something_at_least_32_characters_long"
        ports:
        - containerPort: 5601
          name: ui
          protocol: TCP
        volumeMounts:
        - name: config
          mountPath: /usr/share/kibana/config/kibana.yml
          subPath: kibana.yml
        - name: data
          mountPath: /usr/share/kibana/data
        readinessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 60
          timeoutSeconds: 10
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 120
          timeoutSeconds: 10
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 5
      volumes:
      - name: config
        configMap:
          name: kibana-config
      - name: data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: kibana
  namespace: logging
  labels:
    app: kibana
spec:
  type: ClusterIP
  ports:
  - port: 5601
    targetPort: ui
    protocol: TCP
    name: ui
  selector:
    app: kibana
---
apiVersion: v1
kind: Service
metadata:
  name: kibana-nodeport
  namespace: logging
  labels:
    app: kibana
spec:
  type: NodePort
  ports:
  - port: 5601
    targetPort: ui
    nodePort: 30561
    protocol: TCP
    name: ui
  selector:
    app: kibana
