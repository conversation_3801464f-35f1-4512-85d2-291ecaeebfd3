apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  labels:
    app: user-service
    tier: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        tier: microservice
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
        sidecar.istio.io/inject: "false"
    spec:
      containers:
      - name: user-service
        image: user-service:latest
        imagePullPolicy: Never  # 用于本地构建的镜像
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: JAEGER_AGENT_HOST
          value: "jaeger-agent.tracing.svc.cluster.local"
        - name: JAEGER_AGENT_PORT
          value: "6831"
        - name: JAEGER_SERVICE_NAME
          value: "user-service"
        - name: ORDER_SERVICE_URL
          value: "http://order-service:8080"
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 200m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: log-volume
          mountPath: /var/log
      volumes:
      - name: log-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  labels:
    app: user-service
spec:
  selector:
    app: user-service
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: user-service-config
  labels:
    app: user-service
data:
  app.properties: |
    # 用户服务配置
    server.port=8080
    logging.level.root=INFO
    logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
    
    # Jaeger 配置
    jaeger.service-name=user-service
    jaeger.sampler.type=const
    jaeger.sampler.param=1
    jaeger.reporter.log-spans=true
    
    # 业务配置
    user.service.max-users=1000
    user.service.cache-ttl=300
